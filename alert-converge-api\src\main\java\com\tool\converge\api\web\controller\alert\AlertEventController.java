package com.tool.converge.api.web.controller.alert;

import cn.hutool.http.server.HttpServerResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tool.converge.business.alert.AlertEventService;
import com.tool.converge.repository.domain.alert.bo.AlertEventExportBo;
import com.tool.converge.repository.domain.alert.bo.AlertEventSdkBO;
import com.tool.converge.repository.domain.alert.bo.AlertEventReportBO;
import io.swagger.v3.oas.annotations.Operation;
import com.hzed.structure.log.annotation.PrintLog;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.web.bind.annotation.*;
import com.hzed.structure.tool.annotation.ApiResponse;
import com.tool.converge.repository.domain.alert.bo.AlertEventQueryParamsBO;
import com.tool.converge.repository.domain.alert.vo.AlertEventPageVO;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.time.LocalDateTime;

/**
 * <p>
 * 预警事件 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10 09:19:00
 */
@Tag(name = "预警事件")
@RestController
@RequestMapping("/alertEvent")
public class AlertEventController {

    @Resource
    private AlertEventService alertEventService;

    @ApiResponse
    @Operation(summary = "分页查询预警事件")
    @PrintLog("分页查询预警事件")
    @GetMapping("/page")
    public IPage<AlertEventPageVO> page(@Valid @ParameterObject AlertEventQueryParamsBO queryParamsBO) {
        return alertEventService.getPageInfo(queryParamsBO);
    }

    @ApiResponse
    @Operation(summary = "上报预警事件")
    @PrintLog("上报预警事件")
    @PostMapping("/submit")
    public LocalDateTime submit(@Valid @RequestBody AlertEventSdkBO submitEventBO) {
        return alertEventService.submit(submitEventBO);
    }

    @ApiResponse
    @Operation(summary = "通用预警事件上报")
    @PrintLog("通用预警事件上报")
    @PostMapping("/report")
    public LocalDateTime report(@Valid @RequestBody AlertEventReportBO reportEventBO) {
        return alertEventService.report(reportEventBO);
    }

    @ApiResponse
    @Operation(summary = "导出预警事件")
    @PrintLog("导出预警事件")
    @PostMapping("/export")
    public void export(@RequestBody @Valid AlertEventExportBo alertEventExportBo, HttpServletResponse httpServletResponse) {
        alertEventService.export(alertEventExportBo, httpServletResponse);
    }

}
