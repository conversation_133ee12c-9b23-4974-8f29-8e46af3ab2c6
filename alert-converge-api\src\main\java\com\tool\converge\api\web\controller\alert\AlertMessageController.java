package com.tool.converge.api.web.controller.alert;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tool.converge.business.alert.AlertMessageService;
import com.tool.converge.repository.domain.alert.db.AlertEventDO;
import io.swagger.v3.oas.annotations.Operation;
import com.hzed.structure.log.annotation.PrintLog;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.web.bind.annotation.*;
import com.hzed.structure.tool.annotation.ApiResponse;
import com.tool.converge.repository.domain.alert.bo.AlertMessageSaveBO;
import com.tool.converge.repository.domain.alert.bo.AlertMessageUpdateBO;
import com.tool.converge.repository.domain.alert.bo.AlertMessageQueryParamsBO;
import com.tool.converge.repository.domain.alert.bo.AlertMessageExportBO;
import com.tool.converge.repository.domain.alert.vo.AlertMessagePageVO;
import com.tool.converge.repository.domain.alert.vo.AlertMessageDetailVO;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * <p>
 * 预警消息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Tag(name = "预警消息")
@RestController
@RequestMapping("/alertMessage")
public class AlertMessageController {

    @Resource
    private AlertMessageService alertMessageService;

    @ApiResponse
    @Operation(summary = "分页查询预警消息")
    @PrintLog("分页查询预警消息")
    @GetMapping("/page")
    public IPage<AlertMessagePageVO> page(@Valid @ParameterObject AlertMessageQueryParamsBO queryParamsBO) {
        return alertMessageService.getPageInfo(queryParamsBO);
    }

    @ApiResponse
    @Operation(summary = "获取预警消息详情")
    @PrintLog("获取预警消息详情")
    @GetMapping("/detail/{id}")
    public AlertMessageDetailVO detail(@PathVariable Long id) {
        return alertMessageService.getInfo(id);
    }

    @ApiResponse
    @Operation(summary = "导出预警消息")
    @PrintLog("导出预警消息")
    @PostMapping("/export")
    public void export(@RequestBody @Valid AlertMessageExportBO alertMessageExportBO, HttpServletResponse httpServletResponse) {
        alertMessageService.export(alertMessageExportBO, httpServletResponse);
    }

    @ApiResponse
    @Operation(summary = "发送预警消息")
    @PrintLog("发送预警消息")
    @PostMapping("/send")
    public void send(@RequestBody AlertEventDO alertEventDO) {
        alertMessageService.send(alertEventDO);
    }

}
