package com.tool.converge.api.web.controller.capital;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import com.hzed.structure.log.annotation.PrintLog;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.web.bind.annotation.*;
import com.hzed.structure.tool.annotation.ApiResponse;
import com.tool.converge.business.capital.CapitalService;
import com.tool.converge.repository.domain.capital.bo.CapitalQueryParamsBO;
import com.tool.converge.repository.domain.capital.vo.CapitalPageVO;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 资方表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-06 10:55:56
 */
@Tag(name = "资方表")
@RestController
@RequestMapping("/capital")
public class CapitalController {

    @Resource
    private CapitalService capitalService;


    @ApiResponse
    @Operation(summary = "分页查询资方表")
    @PrintLog("分页查询资方表")
    @GetMapping("/page")
    public IPage<CapitalPageVO> page(@Valid @ParameterObject CapitalQueryParamsBO queryParamsBO) {
        return capitalService.getPageInfo(queryParamsBO);
    }



}
