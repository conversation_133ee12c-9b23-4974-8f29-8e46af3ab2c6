package com.tool.converge.api.web.controller.rules;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import com.hzed.structure.log.annotation.PrintLog;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.web.bind.annotation.*;
import com.hzed.structure.tool.annotation.ApiResponse;
import com.tool.converge.business.rules.RulesService;
import com.tool.converge.repository.domain.rules.bo.RulesSaveBO;
import com.tool.converge.repository.domain.rules.bo.RulesUpdateBO;
import com.tool.converge.repository.domain.rules.bo.RulesQueryParamsBO;
import com.tool.converge.repository.domain.rules.vo.RulesDetailVO;
import com.tool.converge.repository.domain.rules.vo.RulesPageVO;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 规则表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04 16:55:49
 */
@Tag(name = "规则表")
@RestController
@RequestMapping("/rules")
public class RulesController {

    @Resource
    private RulesService rulesService;

    @ApiResponse
    @Operation(summary = "根据id查询规则表")
    @PrintLog("根据id查询规则表")
    @GetMapping("/info/{id}")
    public RulesDetailVO info(@PathVariable("id") Long id) {
        return rulesService.getInfo(id);
    }

    @ApiResponse
    @Operation(summary = "分页查询规则表")
    @PrintLog("分页查询规则表")
    @GetMapping("/page")
    public IPage<RulesPageVO> page(@Valid @ParameterObject RulesQueryParamsBO queryParamsBO) {
        return rulesService.getPageInfo(queryParamsBO);
    }

    @ApiResponse
    @Operation(summary = "保存规则表")
    @PrintLog("保存规则表")
    @PostMapping("/save")
    public Boolean save(@RequestBody @Valid RulesSaveBO saveBO) {
        return rulesService.saveInfo(saveBO);
    }

    @ApiResponse
    @Operation(summary = "修改规则表")
    @PrintLog("修改规则表")
    @PostMapping("/update")
    public Boolean update(@RequestBody @Valid RulesUpdateBO updateBO) {
        return rulesService.updateInfo(updateBO);
    }

    @ApiResponse
    @Operation(summary = "删除规则表")
    @PrintLog("删除规则表")
    @PostMapping("/del/{id}")
    public Boolean del(@PathVariable("id") Long id) {
        return rulesService.delInfo(id);
    }

    @ApiResponse
    @Operation(summary = "更新规则状态")
    @PrintLog("更新规则状态")
    @PostMapping("/updateStatus/{id}")
    public Boolean updateStatus(@PathVariable("id") Long id) {
        return rulesService.updateStatus(id);
    }
}
