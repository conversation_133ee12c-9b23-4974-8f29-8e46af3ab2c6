package com.tool.converge.api.web.controller.source;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tool.converge.business.source.DataSourceService;
import com.tool.converge.repository.domain.source.bo.ExistsSystemCodeBo;
import io.swagger.v3.oas.annotations.Operation;
import com.hzed.structure.log.annotation.PrintLog;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.web.bind.annotation.*;
import com.hzed.structure.tool.annotation.ApiResponse;
import com.tool.converge.repository.domain.source.bo.DataSourceSaveBO;
import com.tool.converge.repository.domain.source.bo.DataSourceUpdateBO;
import com.tool.converge.repository.domain.source.bo.DataSourceQueryParamsBO;
import com.tool.converge.repository.domain.source.vo.DataSourcePageVO;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Set;

/**
 * <p>
 * 数据源 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10 11:00:59
 */
@Tag(name = "数据源")
@RestController
@RequestMapping("/dataSource")
public class DataSourceController {

    @Resource
    private DataSourceService dataSourceService;

    @ApiResponse
    @Operation(summary = "分页查询数据源")
    @PrintLog("分页查询数据源")
    @GetMapping("/page")
    public IPage<DataSourcePageVO> page(@Valid @ParameterObject DataSourceQueryParamsBO queryParamsBO) {
        return dataSourceService.getPageInfo(queryParamsBO);
    }

    @ApiResponse
    @Operation(summary = "保存数据源")
    @PrintLog("保存数据源")
    @PostMapping("/save")
    public Boolean save(@RequestBody @Valid DataSourceSaveBO saveBO) {
        return dataSourceService.saveInfo(saveBO);
    }


    @ApiResponse
    @Operation(summary = "删除数据源")
    @PrintLog("删除数据源")
    @PostMapping("/del/{id}")
    public Boolean del(@PathVariable("id") Long id) {
        return dataSourceService.delInfo(id);
    }

    @ApiResponse
    @Operation(summary = "修改数据源")
    @PrintLog("修改数据源")
    @PostMapping("/update")
    public Boolean update(@RequestBody @Valid DataSourceUpdateBO dataSourceUpdateBO) {
        return dataSourceService.updateInfo(dataSourceUpdateBO);
    }

    @ApiResponse
    @Operation(summary = "判断系统编号是否存在")
    @PrintLog("判断系统编号是否存在")
    @GetMapping("/exists")
    public Boolean existsSystemCode(@Valid @ParameterObject ExistsSystemCodeBo existsSystemCodeBo) {
        return dataSourceService.existsSystemCode(existsSystemCodeBo.getSystemCode());
    }

    @ApiResponse
    @Operation(summary = "获取所有的系统名称")
    @PrintLog("获取所有的系统名称")
    @GetMapping("/getAllSystemName")
    public Set<String> getAllSystemName() {
        return dataSourceService.getAllSystemName();
    }
}
