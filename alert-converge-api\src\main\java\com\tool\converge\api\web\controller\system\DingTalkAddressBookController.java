package com.tool.converge.api.web.controller.system;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzed.structure.log.annotation.PrintLog;
import com.hzed.structure.tool.annotation.ApiResponse;
import com.tool.converge.business.system.DingTalkDeptService;
import com.tool.converge.business.system.DingTalkUserService;
import com.tool.converge.repository.domain.system.bo.DingTalkUserQueryParamsBO;
import com.tool.converge.repository.domain.system.vo.DingTalkDeptDetailVO;
import com.tool.converge.repository.domain.system.vo.DingTalkUserPageVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description 钉钉通讯录
 * @date 2025/8/7 9:26
 */
@Tag(name = "钉钉通讯录")
@RestController
@RequestMapping("/dingTalk/addressBook")
public class DingTalkAddressBookController {

    @Resource
    private DingTalkDeptService dingTalkDeptService;

    @Resource
    private DingTalkUserService dingTalkUserService;

    @ApiResponse
    @Operation(summary = "获取钉钉部门")
    @PrintLog("获取钉钉部门")
    @GetMapping("/dept")
    public List<DingTalkDeptDetailVO> dept() {
        return dingTalkDeptService.allDept();
    }

    @ApiResponse
    @Operation(summary = "分页获取钉钉用户")
    @PrintLog("分页获取钉钉用户")
    @GetMapping("/user/page")
    public IPage<DingTalkUserPageVO> userPage(@ParameterObject DingTalkUserQueryParamsBO bo) {
        return dingTalkUserService.getPageInfo(bo);
    }

    @ApiResponse
    @Operation(summary = "同步钉钉部门和用户")
    @PrintLog("同步钉钉部门和用户")
    @GetMapping("/sync")
    public void sync() {
        dingTalkUserService.sync();
    }

}
