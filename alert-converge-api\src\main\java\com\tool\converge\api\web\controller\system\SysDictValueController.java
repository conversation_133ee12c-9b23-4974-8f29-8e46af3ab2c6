package com.tool.converge.api.web.controller.system;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzed.structure.log.annotation.PrintLog;
import com.hzed.structure.tool.annotation.ApiResponse;
import com.tool.converge.business.system.SysDictValueService;
import com.tool.converge.repository.domain.system.bo.SysDictValueQueryParamsBO;
import com.tool.converge.repository.domain.system.bo.SysDictValueSaveBO;
import com.tool.converge.repository.domain.system.bo.SysDictValueUpdateBO;
import com.tool.converge.repository.domain.system.db.SysDictValueDO;
import com.tool.converge.repository.domain.system.vo.SysDictValuePageVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 字典配置值 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-04
 */
@Tag(name = "字典配置值")
@RestController
@RequestMapping("/sysDictValue")
public class SysDictValueController {

    @Resource
    SysDictValueService sysDictValueService;

    @ApiResponse
    @Operation(summary = "字典配置值分页列表查询")
    @PrintLog("字典配置值分页列表查询")
    @GetMapping("/page")
    public IPage<SysDictValuePageVO> page(@ParameterObject @Valid SysDictValueQueryParamsBO bo) {
        return sysDictValueService.selectPage(bo);
    }

    @ApiResponse
    @Operation(summary = "根据keyName查询字典配置值已启用列表")
    @PrintLog("根据keyName查询字典配置值列表")
    @GetMapping("/list/{keyName}")
    public List<SysDictValueDO> listByKeyId(@PathVariable("keyName") String keyName) {
        return sysDictValueService.listByKeyName(keyName);
    }

    @ApiResponse
    @Operation(summary = "保存字典配置值")
    @PrintLog("保存字典配置值")
    @PostMapping("/save")
    public Boolean save(@RequestBody @Valid SysDictValueSaveBO saveBO) {
        return sysDictValueService.saveInfo(saveBO);
    }

    @ApiResponse
    @Operation(summary = "修改字典配置值")
    @PrintLog("修改字典配置值")
    @PostMapping("/update")
    public Boolean update(@RequestBody @Valid SysDictValueUpdateBO updateBO) {
        return sysDictValueService.updateInfo(updateBO);
    }

    @ApiResponse
    @Operation(summary = "删除字典配置值")
    @PrintLog("删除字典配置值")
    @PostMapping("/del/{id}")
    public Boolean del(@PathVariable("id") Long id) {
        return sysDictValueService.delInfo(id);
    }



}
