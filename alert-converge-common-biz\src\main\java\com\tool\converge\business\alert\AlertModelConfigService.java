package com.tool.converge.business.alert;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tool.converge.repository.domain.alert.db.AlertModelConfigDO;
import com.tool.converge.repository.domain.alert.bo.AlertModelConfigSaveBO;
import com.tool.converge.repository.domain.alert.bo.AlertModelConfigUpdateBO;
import com.tool.converge.repository.domain.alert.bo.AlertModelConfigQueryParamsBO;
import com.tool.converge.repository.domain.alert.vo.AlertModelConfigDetailVO;
import com.tool.converge.repository.domain.alert.vo.AlertModelConfigPageVO;
import com.tool.converge.repository.domain.alert.vo.AlertMessageFullInfoCompositeVO;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05 10:41:50
 */
public interface AlertModelConfigService extends IService<AlertModelConfigDO> {

    /**
     * 添加信息
     *
     * @param saveBO
     * @return
     */
    Boolean saveInfo(AlertModelConfigSaveBO saveBO);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    Boolean delInfo(Long id);

    /**
     * 修改信息
     *
     * @param updateBO
     * @return
     */
    Boolean updateInfo(AlertModelConfigUpdateBO updateBO);

    /**
     * 通过ID获取信息
     *
     * @param id
     * @return
     */
    AlertModelConfigDetailVO getInfo(Long id);

    /**
     * 分页获取列表
     *
     * @param queryParamsBO
     * @return
     */
    IPage<AlertModelConfigPageVO> getPageInfo(AlertModelConfigQueryParamsBO queryParamsBO);

    /**
     * 根据模型ID获取完整的预警配置信息(包括通知人员、规则等关联信息)
     *
     * @param modelId 模型ID
     * @return 完整的预警配置信息
     */
    AlertMessageFullInfoCompositeVO getFullInfoByModelId(Long modelId);

}
