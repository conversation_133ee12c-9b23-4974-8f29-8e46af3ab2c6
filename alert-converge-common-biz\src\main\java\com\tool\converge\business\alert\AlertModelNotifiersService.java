package com.tool.converge.business.alert;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tool.converge.repository.domain.alert.db.AlertModelNotifiersDO;
import com.tool.converge.repository.domain.alert.bo.AlertModelNotifiersSaveBO;
import com.tool.converge.repository.domain.alert.bo.AlertModelNotifiersUpdateBO;
import com.tool.converge.repository.domain.alert.bo.AlertModelNotifiersQueryParamsBO;
import com.tool.converge.repository.domain.alert.vo.AlertModelNotifiersDetailVO;
import com.tool.converge.repository.domain.alert.vo.AlertModelNotifiersPageVO;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05 10:41:50
 */
public interface AlertModelNotifiersService extends IService<AlertModelNotifiersDO> {

    /**
     * 添加信息
     *
     * @param saveBO
     * @return
     */
    Boolean saveInfo(AlertModelNotifiersSaveBO saveBO);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    Boolean delInfo(Long id);

    /**
     * 修改信息
     *
     * @param updateBO
     * @return
     */
    Boolean updateInfo(AlertModelNotifiersUpdateBO updateBO);

    /**
     * 通过ID获取信息
     *
     * @param id
     * @return
     */
    AlertModelNotifiersDetailVO getInfo(Long id);

    /**
     * 分页获取列表
     *
     * @param queryParamsBO
     * @return
     */
    IPage<AlertModelNotifiersPageVO> getPageInfo(AlertModelNotifiersQueryParamsBO queryParamsBO);

}
