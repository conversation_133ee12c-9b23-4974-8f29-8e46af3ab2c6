package com.tool.converge.business.alert.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzed.structure.common.exception.ServiceException;
import com.tool.converge.business.alert.AlertMessageService;
import com.tool.converge.business.alert.AlertModelConfigService;
import com.tool.converge.business.alert.AlertModelNotifiersService;
import com.tool.converge.business.alert.AlertModelRuleService;
import com.tool.converge.business.alert.AlertModelService;
import com.tool.converge.business.rules.RulesService;
import com.tool.converge.business.system.SysDictValueService;
import com.tool.converge.business.system.SysUserService;
import com.tool.converge.repository.domain.alert.bo.AlertMessageSaveBO;
import com.tool.converge.repository.domain.alert.bo.AlertMessageUpdateBO;
import com.tool.converge.repository.domain.alert.bo.AlertMessageQueryParamsBO;
import com.tool.converge.repository.domain.alert.bo.AlertMessageExportBO;
import com.tool.converge.repository.domain.alert.db.*;
import com.tool.converge.repository.domain.alert.vo.AlertMessageDetailVO;
import com.tool.converge.repository.domain.alert.vo.AlertMessageFullInfoVO;
import com.tool.converge.repository.domain.alert.vo.AlertMessageFullInfoCompositeVO;
import com.tool.converge.repository.domain.alert.vo.AlertMessagePageVO;
import com.tool.converge.repository.domain.rules.db.RulesDO;
import com.tool.converge.repository.domain.system.db.SysDictValueDO;
import com.tool.converge.repository.domain.alert.vo.AlertMessageExportVO;
import com.tool.converge.common.utils.ExcelUtils;
import com.tool.converge.repository.domain.alert.db.AlertEventDO;
import com.tool.converge.repository.domain.alert.db.AlertMessageDO;
import com.tool.converge.repository.domain.alert.db.AlertModelDO;
import com.tool.converge.repository.mapper.alert.AlertMessageMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 预警消息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Slf4j
@Service
public class AlertMessageServiceImpl extends ServiceImpl<AlertMessageMapper, AlertMessageDO> implements AlertMessageService {

    @Resource
    private AlertMessageMapper alertMessageMapper;

    @Resource
    private SysDictValueService sysDictValueService;

    @Resource
    @Lazy
    private AlertModelService alertModelService;

    @Resource
    private AlertModelConfigService alertModelConfigService;

    @Resource
    private AlertModelRuleService alertModelRuleService;

    @Resource
    private AlertModelNotifiersService alertModelNotifiersService;

    @Resource
    private RulesService rulesService;
@Resource
    private SysUserService sysUserService;

    @Override
    public Boolean saveInfo(AlertMessageSaveBO saveBO) {
        AlertMessageDO entity = new AlertMessageDO();
        BeanUtils.copyProperties(saveBO, entity);
        return save(entity);
    }

    @Override
    public AlertMessageDetailVO getInfo(Long id) {
        if (id == null) {
            throw new ServiceException("ID不能为空");
        }
        AlertMessageDO entity = getById(id);
        return AlertMessageDetailVO.of(entity);
    }

    @Override
    public IPage<AlertMessagePageVO> getPageInfo(AlertMessageQueryParamsBO queryParamsBO) {
        IPage<AlertMessageDO> page = new Page<>(queryParamsBO.getPageNum(), queryParamsBO.getPageSize());
        
        // 执行分页查询
        IPage<AlertMessageDO> resultPage = alertMessageMapper.selectMessagePage(page, queryParamsBO);
        
        // 转换为VO
        List<AlertMessagePageVO> voList = resultPage.getRecords().stream()
                .map(AlertMessagePageVO::of)
                .collect(Collectors.toList());
        
        IPage<AlertMessagePageVO> voPage = new Page<>(resultPage.getCurrent(), resultPage.getSize(), resultPage.getTotal());
        voPage.setRecords(voList);
        
        return voPage;
    }

    @Override
    public void export(AlertMessageExportBO alertMessageExportBO, HttpServletResponse httpServletResponse) {
        // 根据ID列表查询预警消息数据
        List<AlertMessageDO> alertMessageList = listByIds(alertMessageExportBO.getIds());

        if (alertMessageList == null || alertMessageList.isEmpty()) {
            throw new ServiceException("没有找到要导出的数据");
        }

        Map<String,String> businessTypeMap = sysDictValueService.listByKeyName("business_type").stream()
                .collect(Collectors.toMap(SysDictValueDO::getValue, SysDictValueDO::getLabel));
        Map<String,String> alertTypeMap = sysDictValueService.listByKeyName("alert_type").stream()
                .collect(Collectors.toMap(SysDictValueDO::getValue, SysDictValueDO::getLabel));
        Map<String,String> warnLevelMap = sysDictValueService.listByKeyName("warn_level").stream()
                .collect(Collectors.toMap(SysDictValueDO::getValue, SysDictValueDO::getLabel));

        // 转换为导出VO
        List<AlertMessageExportVO> exportVOList = alertMessageList.stream()
                .map(AlertMessageExportVO::of)
                .map(vo -> {
                    vo.setBusinessType(businessTypeMap.get(vo.getBusinessType())!=null?businessTypeMap.get(vo.getBusinessType()):vo.getBusinessType());
                    vo.setAlertType(alertTypeMap.get(vo.getAlertType())!=null?alertTypeMap.get(vo.getAlertType()):vo.getAlertType());
                    vo.setWarnLevel(warnLevelMap.get(vo.getWarnLevel())!=null?warnLevelMap.get(vo.getWarnLevel()):vo.getWarnLevel());
                    return vo;
                })
                .collect(Collectors.toList());

        // 生成文件名：预警消息_yyyyMMddHHmmss
        String fileName = "预警消息_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));

        // 导出Excel
        ExcelUtils.writeExcel(exportVOList, AlertMessageExportVO.class, alertMessageExportBO.getOrderByColumns(), fileName, httpServletResponse);
    }

    @Override
    public void send(AlertEventDO alertEventDO) {
        //查出事件对应的预警模型
        AlertModelDO alertModelDO = alertModelService.list(
            new LambdaQueryWrapper<AlertModelDO>()
            .eq(AlertModelDO::getModelCode, alertEventDO.getModelCode())
            .eq(AlertModelDO::getDeleted, false)
            ).stream().findFirst().orElse(null);
        if (alertModelDO == null) {
            return;
        }
        //---根据预警模板来判断是否需要发送预警消息---
        //如果不没有配置规则并且没有开启发送消息则无需处理
        if (!alertModelDO.getWarned() || !alertModelDO.getRelated()) {
            return;
        }
        
        // 获取完整的预警信息
        AlertMessageFullInfoVO fullInfo = buildAlertMessageFullInfo(alertEventDO, alertModelDO);
        
        // TODO: 实现预警消息发送逻辑
        // 1. 根据预警方式发送消息(钉钉、短信等)
        // 2. 将预警消息存入数据库
        
    }
    
    /**
     * 构建完整的预警消息信息
     *
     * @param alertEventDO 预警事件
     * @param alertModelDO 预警模型
     * @return 完整的预警消息信息
     */
    private AlertMessageFullInfoVO buildAlertMessageFullInfo(AlertEventDO alertEventDO, AlertModelDO alertModelDO) {
        AlertMessageFullInfoVO.AlertMessageFullInfoVOBuilder builder = AlertMessageFullInfoVO.builder();
        // 基本信息
        builder.platformName(alertEventDO.getPlatformName())
               .period(alertEventDO.getPeriod())
               .modelName(alertModelDO.getModelName())
               .businessType(alertModelDO.getBusinessType())
               .alertType(alertModelDO.getAlertType())
               .reason(alertEventDO.getReason())
               .createTime(alertEventDO.getCreateTime())
               .related(alertModelDO.getRelated())
               .warned(alertModelDO.getWarned());
        // 通过关联查询一次性获取所有需要的数据
        AlertMessageFullInfoCompositeVO compositeVO = alertModelConfigService.getFullInfoByModelId(alertModelDO.getId());
        if (compositeVO != null && compositeVO.getAlertModelConfig() != null) {
            AlertModelConfigDO alertModelConfigDO = compositeVO.getAlertModelConfig();
            builder.frequency(alertModelConfigDO.getFrequency())
                   .warnType(alertModelConfigDO.getWarnType())
                   .webhook(alertModelConfigDO.getWebhook())
                   .warnContent(alertModelConfigDO.getWarnContent());
            // 获取通知人员信息
            List<AlertModelNotifiersDO> notifiers = compositeVO.getNotifiers();
            // 获取用户详细信息(昵称、钉钉ID、手机号)
            if (notifiers != null && !notifiers.isEmpty()) {
                List<com.tool.converge.repository.domain.system.db.SysUserDO> users = compositeVO.getUsers(); 
                if (users != null && !users.isEmpty()) {
                    Map<Long, com.tool.converge.repository.domain.system.db.SysUserDO> userMap = users.stream()
                        .collect(Collectors.toMap(com.tool.converge.repository.domain.system.db.SysUserDO::getUserId, user -> user));
                    List<AlertMessageFullInfoVO.NotifierInfoVO> notifierInfoVOs = notifiers.stream()
                        .map(notifier -> {
                            com.tool.converge.repository.domain.system.db.SysUserDO user = userMap.get(notifier.getUserId());
                            if (user != null) {
                                return AlertMessageFullInfoVO.NotifierInfoVO.builder()
                                    .userId(user.getUserId())
                                    .nickName(user.getNickName())
                                    .dingtalkUserId(user.getDingtalkUserId())
                                    .phonenumber(user.getPhonenumber())
                                    .build();
                            } else {
                                return AlertMessageFullInfoVO.NotifierInfoVO.builder()
                                    .userId(notifier.getUserId())
                                    .build();
                            }
                        })
                        .collect(Collectors.toList());
                    builder.notifiers(notifierInfoVOs);
                }
            }
            // 获取关联的规则信息
            List<AlertModelRuleDO> modelRules = compositeVO.getModelRules();
            if (modelRules != null && !modelRules.isEmpty()) {
                List<RulesDO> rules = compositeVO.getRules();
                if (rules != null && !rules.isEmpty()) {
                    List<AlertMessageFullInfoVO.RuleConditionVO> ruleConditionVOs = rules.stream()
                        .map(rule -> AlertMessageFullInfoVO.RuleConditionVO.builder()
                            .ruleId(rule.getId())
                            .ruleName(rule.getRuleName())
                            .ruleCode(rule.getRuleCode())
                            .build())
                        .collect(Collectors.toList());
                    
                    builder.ruleConditions(ruleConditionVOs);
                }
            }
        }
        return builder.build();
    }
}
