package com.tool.converge.business.alert.impl;

import com.tool.converge.business.alert.AlertModelNotifiersService;
import com.tool.converge.repository.domain.alert.bo.AlertModelNotifiersSaveBO;
import com.tool.converge.repository.domain.alert.bo.AlertModelNotifiersUpdateBO;
import com.tool.converge.repository.domain.alert.bo.AlertModelNotifiersQueryParamsBO;
import com.tool.converge.repository.domain.alert.vo.AlertModelNotifiersDetailVO;
import com.tool.converge.repository.domain.alert.vo.AlertModelNotifiersPageVO;
import com.tool.converge.repository.domain.alert.db.AlertModelNotifiersDO;
import com.tool.converge.repository.mapper.alert.AlertModelNotifiersMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05 10:41:50
 */
@Slf4j
@Service
public class AlertModelNotifiersServiceImpl extends ServiceImpl<AlertModelNotifiersMapper, AlertModelNotifiersDO> implements AlertModelNotifiersService {

    @Resource
    private AlertModelNotifiersMapper alertModelNotifiersMapper;

    @Override
    public Boolean saveInfo(AlertModelNotifiersSaveBO saveBO){
        AlertModelNotifiersDO entity = new AlertModelNotifiersDO();
        BeanUtils.copyProperties(saveBO, entity);
        return SqlHelper.retBool(alertModelNotifiersMapper.insert(entity));
    }

    @Override
    public Boolean delInfo(Long id){
        return SqlHelper.retBool(alertModelNotifiersMapper.deleteById(id));
    }

    @Override
    public Boolean updateInfo(AlertModelNotifiersUpdateBO updateBO){
        AlertModelNotifiersDO entity = new AlertModelNotifiersDO();
        BeanUtils.copyProperties(updateBO, entity);
        return SqlHelper.retBool(alertModelNotifiersMapper.updateById(entity));
    }

    @Override
    public AlertModelNotifiersDetailVO getInfo(Long id){
        AlertModelNotifiersDO entity = alertModelNotifiersMapper.selectById(id);
        return AlertModelNotifiersDetailVO.of(entity);
    }

    @Override
    public IPage<AlertModelNotifiersPageVO> getPageInfo(AlertModelNotifiersQueryParamsBO queryParamsBO){
        return alertModelNotifiersMapper.selectPage(queryParamsBO.pageInfo(), queryParamsBO.queryWrapper()).convert(AlertModelNotifiersPageVO::of);
    }

}
