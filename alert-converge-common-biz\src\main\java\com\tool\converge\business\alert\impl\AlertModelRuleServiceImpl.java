package com.tool.converge.business.alert.impl;

import com.tool.converge.business.alert.AlertModelRuleService;
import com.tool.converge.repository.domain.alert.db.AlertModelRuleDO;
import com.tool.converge.repository.mapper.alert.AlertModelRuleMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
/**
 * <p>
 * 模型规则关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-06 14:45:26
 */
@Slf4j
@Service
public class AlertModelRuleServiceImpl extends ServiceImpl<AlertModelRuleMapper, AlertModelRuleDO> implements AlertModelRuleService {

    @Resource
    private AlertModelRuleMapper alertModelRuleMapper;

}
