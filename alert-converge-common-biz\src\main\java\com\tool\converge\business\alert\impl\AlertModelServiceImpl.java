package com.tool.converge.business.alert.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzed.structure.common.exception.ServiceException;
import com.tool.converge.business.alert.*;
import com.tool.converge.repository.domain.alert.bo.AlertModelExtendUpdateBo;
import com.tool.converge.repository.domain.alert.bo.AlertModelQueryExtendSaveBo;
import com.tool.converge.repository.domain.alert.bo.AlertModelQueryParamsBO;
import com.tool.converge.repository.domain.alert.db.*;
import com.tool.converge.repository.domain.alert.vo.AlertModelDetailVO;
import com.tool.converge.repository.domain.alert.vo.AlertModelExtendPageVO;
import com.tool.converge.repository.mapper.alert.AlertModelMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 预警模型 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10 09:19:01
 */
@Slf4j
@Service
public class AlertModelServiceImpl extends ServiceImpl<AlertModelMapper, AlertModelDO> implements AlertModelService {

    @Resource
    private AlertModelMapper alertModelMapper;

    @Resource
    private AlertEventService alertEventService;
    @Resource
    private AlertModelConfigService alertModelConfigService;

    @Resource
    private AlertModelNotifiersService alertModelNotifiersService;

    @Resource
    private AlertModelRuleService alertModelRuleService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveInfo(AlertModelQueryExtendSaveBo saveBO){
        AlertModelDO entity = new AlertModelDO();
        if (existsModelCode(saveBO.getModelCode())) {
            throw new ServiceException("预警模型编码已存在");
        }
        BeanUtils.copyProperties(saveBO, entity);
        alertModelMapper.insert(entity);
        if (entity.getId() == null) {
            throw new ServiceException("预警模型编码保存失败");
        }
        if (saveBO.getWarned()) {
            AlertModelConfigDO configDO = new AlertModelConfigDO();
            BeanUtils.copyProperties(saveBO, configDO);
            configDO.setModelId(entity.getId());
            alertModelConfigService.save(configDO);

            if (CollectionUtil.isNotEmpty(saveBO.getNotifiers())) {
                List<AlertModelNotifiersDO> notifiersDos = saveBO.getNotifiers().stream().map(id -> {
                    AlertModelNotifiersDO alertModelNotifiersDO = new AlertModelNotifiersDO();
                    alertModelNotifiersDO.setConfigId(configDO.getId());
                    alertModelNotifiersDO.setUserId(id);
                    return alertModelNotifiersDO;
                }).collect(Collectors.toList());
                alertModelNotifiersService.saveBatch(notifiersDos);
            }
        }
        if (CollectionUtil.isNotEmpty(saveBO.getRuleIds())) {
            alertModelRuleService.remove(new LambdaQueryWrapper<AlertModelRuleDO>().eq(AlertModelRuleDO::getModelId, entity.getId()));
            List<AlertModelRuleDO> modelRuleDos = saveBO.getRuleIds().stream().map(id -> {
                AlertModelRuleDO ruleDO = new AlertModelRuleDO();
                ruleDO.setModelId(entity.getId());
                ruleDO.setRuleId(id);
                return ruleDO;
            }).collect(Collectors.toList());
            alertModelRuleService.saveBatch(modelRuleDos);
        }

        return true;
    }

    @Override
    public Set<String> getAllModelName() {
        List<AlertModelDO> alertModelDoS = alertModelMapper.selectList(new LambdaQueryWrapper<AlertModelDO>().select(AlertModelDO::getModelName));
        return alertModelDoS.stream().map(AlertModelDO::getModelName).collect(Collectors.toSet());
    }

    @Override
    public Boolean delInfo(Long id){
        AlertModelDO alertModelDO = alertModelMapper.selectById(id);
        if (alertModelDO == null) {
            throw new ServiceException("该预警模型不存在");
        }
        if (alertEventService.exists(new LambdaQueryWrapper<AlertEventDO>().eq(AlertEventDO::getModelCode, alertModelDO.getModelCode()))) {
            throw new ServiceException("该预警模型存在引用预警事件，不支持删除");
        }
        return SqlHelper.retBool(alertModelMapper.deleteById(id));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateInfo(AlertModelExtendUpdateBo updateBO){
        AlertModelDO entity = new AlertModelDO();
        BeanUtils.copyProperties(updateBO, entity);
        alertModelMapper.updateById(entity);
        if (updateBO.getWarned()) {
            AlertModelConfigDO dbConfigDo = alertModelConfigService.getOne(new LambdaQueryWrapper<AlertModelConfigDO>().eq(AlertModelConfigDO::getModelId, updateBO.getId()));
            AlertModelConfigDO configDO = new AlertModelConfigDO();
            BeanUtils.copyProperties(updateBO, configDO);
            if (dbConfigDo != null) {
                configDO.setId(dbConfigDo.getId());
                alertModelConfigService.updateById(configDO);
            }else {
                configDO.setModelId(entity.getId());
                alertModelConfigService.save(configDO);
            }
            if (CollectionUtil.isNotEmpty(updateBO.getNotifiers())) {
                // 先移除所有的数据
                alertModelNotifiersService.remove(new LambdaQueryWrapper<AlertModelNotifiersDO>().eq(AlertModelNotifiersDO::getConfigId, configDO.getId()));
                // 在保存新的关联条件
                List<AlertModelNotifiersDO> notifiersDos = updateBO.getNotifiers().stream().map(id -> {
                    AlertModelNotifiersDO alertModelNotifiersDO = new AlertModelNotifiersDO();
                    alertModelNotifiersDO.setConfigId(configDO.getId());
                    alertModelNotifiersDO.setUserId(id);
                    return alertModelNotifiersDO;
                }).collect(Collectors.toList());
                alertModelNotifiersService.saveBatch(notifiersDos);
            }

            if (CollectionUtil.isNotEmpty(updateBO.getRuleIds())) {
                alertModelRuleService.remove(new LambdaQueryWrapper<AlertModelRuleDO>().eq(AlertModelRuleDO::getModelId, entity.getId()));
                List<AlertModelRuleDO> modelRuleDos = updateBO.getRuleIds().stream().map(id -> {
                    AlertModelRuleDO ruleDO = new AlertModelRuleDO();
                    ruleDO.setModelId(entity.getId());
                    ruleDO.setRuleId(id);
                    return ruleDO;
                }).collect(Collectors.toList());
                alertModelRuleService.saveBatch(modelRuleDos);
            }
        }
        return true;
    }

    @Override
    public AlertModelDetailVO getInfo(Long id){
        AlertModelDO entity = alertModelMapper.selectById(id);
        return AlertModelDetailVO.of(entity);
    }

    @Override
    public IPage<AlertModelExtendPageVO> getPageInfo(AlertModelQueryParamsBO queryParamsBO){
        if (StringUtils.isBlank(queryParamsBO.getOrderFields())) {
            queryParamsBO.setOrderFields("createTime");
        }
        if (StringUtils.isBlank(queryParamsBO.getOrderRules())) {
            queryParamsBO.setOrderRules("desc");
        }
        IPage<AlertModelExtendPageVO> pages = alertModelMapper.selectPage(queryParamsBO.pageInfo(), queryParamsBO.queryWrapper()).convert(AlertModelExtendPageVO::of);
        List<AlertModelExtendPageVO> records = pages.getRecords();
        Set<Long> modelIds = records.stream().map(AlertModelExtendPageVO::getId).collect(Collectors.toSet());
        if (CollectionUtil.isNotEmpty(modelIds)) {
            Map<Long, AlertModelConfigDO> configMap = new HashMap<>(16);
            Map<Long, List<Long>> notifiersMap = new HashMap<>(16);
            List<AlertModelConfigDO> list = alertModelConfigService.list(new LambdaQueryWrapper<AlertModelConfigDO>().in(AlertModelConfigDO::getModelId, modelIds));
            if (CollectionUtil.isNotEmpty(list)) {
                 configMap = list.stream()
                        .collect(Collectors.toMap(AlertModelConfigDO::getModelId, Function.identity(), (a, b) -> a));

                Set<Long> configIds = list.stream().map(AlertModelConfigDO::getId).collect(Collectors.toSet());
                // 查询关联通知人员
                List<AlertModelNotifiersDO> notifiersDos = alertModelNotifiersService.list(new LambdaQueryWrapper<AlertModelNotifiersDO>().in(AlertModelNotifiersDO::getConfigId, configIds));
                notifiersMap = notifiersDos.stream()
                        .collect(Collectors.groupingBy(
                                AlertModelNotifiersDO::getConfigId,
                                Collectors.mapping(AlertModelNotifiersDO::getUserId, Collectors.toList())
                        ));
            }
            // 查询关联规则
            List<AlertModelRuleDO> ruleDos = alertModelRuleService.list(new LambdaQueryWrapper<AlertModelRuleDO>().in(AlertModelRuleDO::getModelId, modelIds));
            Map<Long, List<Long>> ruleIdsMap = ruleDos.stream()
                    .collect(Collectors.groupingBy(
                            AlertModelRuleDO::getModelId,
                            Collectors.mapping(AlertModelRuleDO::getRuleId, Collectors.toList())
                    ));
            Map<Long, AlertModelConfigDO> finalConfigMap = configMap;
            Map<Long, List<Long>> finalNotifiersMap = notifiersMap;
            records.forEach(record -> {
                AlertModelConfigDO configDO = finalConfigMap.get(record.getId());
                if (configDO == null) {
                    return ;
                }
                Long modelId = record.getId();
                // 会把id 替换掉
                BeanUtils.copyProperties(configDO, record);
                record.setId(modelId);
                record.setConfigId(configDO.getId());
                record.setNotifiers(finalNotifiersMap.get(configDO.getId()));
                record.setRuleIds(ruleIdsMap.get(modelId));
            });
        }

        return pages;
    }

    private Boolean existsModelCode(String modelCode) {
        List<AlertModelDO> alertModelDoS = alertModelMapper.selectList(new LambdaQueryWrapper<AlertModelDO>().eq(AlertModelDO::getModelCode, modelCode));
        return !alertModelDoS.isEmpty();
    }
}
