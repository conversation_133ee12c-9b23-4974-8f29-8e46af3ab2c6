package com.tool.converge.business.capital;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tool.converge.repository.domain.capital.db.CapitalDO;
import com.tool.converge.repository.domain.capital.bo.CapitalSaveBO;
import com.tool.converge.repository.domain.capital.bo.CapitalUpdateBO;
import com.tool.converge.repository.domain.capital.bo.CapitalQueryParamsBO;
import com.tool.converge.repository.domain.capital.vo.CapitalDetailVO;
import com.tool.converge.repository.domain.capital.vo.CapitalPageVO;

import java.util.List;

/**
 * <p>
 * 资方表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-06 10:55:56
 */
public interface CapitalService extends IService<CapitalDO> {


    /**
     * 分页获取列表
     *
     * @param queryParamsBO
     * @return
     */
    IPage<CapitalPageVO> getPageInfo(CapitalQueryParamsBO queryParamsBO);


    /**
     * 物理删除资方信息
     * @param capitalToDelete
     */
    void physicalDeleteBatchIds(List<Long> capitalToDelete);
}
