package com.tool.converge.business.capital.impl;

import com.tool.converge.repository.domain.capital.bo.CapitalQueryParamsBO;
import com.tool.converge.repository.domain.capital.vo.CapitalPageVO;
import com.tool.converge.repository.domain.capital.db.CapitalDO;
import com.tool.converge.repository.mapper.capital.CapitalMapper;
import com.tool.converge.business.capital.CapitalService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 资方表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-06 10:55:56
 */
@Slf4j
@Service
public class CapitalServiceImpl extends ServiceImpl<CapitalMapper, CapitalDO> implements CapitalService {

    @Resource
    private CapitalMapper capitalMapper;


    @Override
    public IPage<CapitalPageVO> getPageInfo(CapitalQueryParamsBO queryParamsBO){
        return capitalMapper.selectPage(queryParamsBO.pageInfo(), queryParamsBO.queryWrapper()).convert(CapitalPageVO::of);
    }

    @Override
    public void physicalDeleteBatchIds(List<Long> capitalToDelete) {
        capitalMapper.physicalDeleteBatchIds(capitalToDelete);
    }

}
