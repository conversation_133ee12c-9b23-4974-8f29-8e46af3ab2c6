package com.tool.converge.business.dingtalk.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @description 钉钉配置
 * @date 2025/8/5 10:47
 */
@Data
@Configuration
@NoArgsConstructor
@AllArgsConstructor
@ConfigurationProperties(prefix = "dingtalk")
public class DingTalkConfig {
    /**
     * 全民钱包应用的唯一标识key
     */
    private String appKey;
    /**
     * 全民钱包应用的密钥
     */
    private String appSecret;
}
