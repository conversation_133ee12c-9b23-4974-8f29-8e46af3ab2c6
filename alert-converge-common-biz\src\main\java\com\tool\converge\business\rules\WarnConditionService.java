package com.tool.converge.business.rules;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tool.converge.repository.domain.rules.db.WarnConditionDO;
import com.tool.converge.repository.domain.rules.bo.WarnConditionQueryParamsBO;
import com.tool.converge.repository.domain.rules.vo.WarnConditionPageVO;

/**
 * <p>
 * 预警条件表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04 16:55:50
 */
public interface WarnConditionService extends IService<WarnConditionDO> {
    /**
     * 分页获取列表
     *
     * @param queryParamsBO
     * @return
     */
    IPage<WarnConditionPageVO> getPageInfo(WarnConditionQueryParamsBO queryParamsBO);

}