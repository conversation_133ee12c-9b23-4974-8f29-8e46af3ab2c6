package com.tool.converge.business.rules.impl;

import com.tool.converge.repository.domain.rules.bo.WarnConditionQueryParamsBO;
import com.tool.converge.repository.domain.rules.vo.WarnConditionPageVO;
import com.tool.converge.repository.domain.rules.db.WarnConditionDO;
import com.tool.converge.repository.mapper.rules.WarnConditionMapper;
import com.tool.converge.business.rules.WarnConditionService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
/**
 * <p>
 * 预警条件表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04 16:55:50
 */
@Slf4j
@Service
public class WarnConditionServiceImpl extends ServiceImpl<WarnConditionMapper, WarnConditionDO> implements WarnConditionService {

    @Resource
    private WarnConditionMapper warnConditionMapper;

    @Override
    public IPage<WarnConditionPageVO> getPageInfo(WarnConditionQueryParamsBO queryParamsBO){
        return warnConditionMapper.selectPage(queryParamsBO.pageInfo(), queryParamsBO.queryWrapper()).convert(WarnConditionPageVO::of);
    }

}
