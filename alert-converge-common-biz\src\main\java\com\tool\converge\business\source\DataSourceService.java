package com.tool.converge.business.source;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tool.converge.repository.domain.source.db.DataSourceDO;
import com.tool.converge.repository.domain.source.bo.DataSourceSaveBO;
import com.tool.converge.repository.domain.source.bo.DataSourceUpdateBO;
import com.tool.converge.repository.domain.source.bo.DataSourceQueryParamsBO;
import com.tool.converge.repository.domain.source.vo.DataSourceDetailVO;
import com.tool.converge.repository.domain.source.vo.DataSourcePageVO;

import java.util.Set;

/**
 * <p>
 * 数据源 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10 11:00:59
 */
public interface DataSourceService extends IService<DataSourceDO> {

    /**
     * 添加信息
     *
     * @param saveBO
     * @return
     */
    Boolean saveInfo(DataSourceSaveBO saveBO);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    Boolean delInfo(Long id);

    /**
     * 判断系统编号是否存在
     *
     * @param systemCode
     * @return
     */
    Boolean existsSystemCode(String systemCode);
    /**
     * 获取所有的系统名称
     *
     * @return
     */
    Set<String> getAllSystemName();

    /**
     * 修改信息
     *
     * @param updateBO
     * @return
     */
    Boolean updateInfo(DataSourceUpdateBO updateBO);


    /**
     * 分页获取列表
     *
     * @param queryParamsBO
     * @return
     */
    IPage<DataSourcePageVO> getPageInfo(DataSourceQueryParamsBO queryParamsBO);

}
