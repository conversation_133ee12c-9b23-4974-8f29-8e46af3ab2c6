package com.tool.converge.business.source.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzed.structure.common.exception.ServiceException;
import com.tool.converge.business.alert.AlertEventService;
import com.tool.converge.business.source.DataSourceService;
import com.tool.converge.repository.domain.alert.db.AlertEventDO;
import com.tool.converge.repository.domain.source.bo.DataSourceSaveBO;
import com.tool.converge.repository.domain.source.bo.DataSourceUpdateBO;
import com.tool.converge.repository.domain.source.bo.DataSourceQueryParamsBO;
import com.tool.converge.repository.domain.source.vo.DataSourceDetailVO;
import com.tool.converge.repository.domain.source.vo.DataSourcePageVO;
import com.tool.converge.repository.domain.source.db.DataSourceDO;
import com.tool.converge.repository.mapper.source.DataSourceMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 数据源 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10 11:00:59
 */
@Slf4j
@Service
public class DataSourceServiceImpl extends ServiceImpl<DataSourceMapper, DataSourceDO> implements DataSourceService {

    @Resource
    private DataSourceMapper dataSourceMapper;

    @Resource
    private AlertEventService alertEventService;
    @Override
    public Boolean saveInfo(DataSourceSaveBO saveBO){
        if (existsSystemCode(saveBO.getSystemCode())) {
            throw new ServiceException("系统编号已存在");
        }
        DataSourceDO entity = new DataSourceDO();
        BeanUtils.copyProperties(saveBO, entity);
        return SqlHelper.retBool(dataSourceMapper.insert(entity));
    }

    @Override
    public Boolean delInfo(Long id){
        DataSourceDO sourceDO = dataSourceMapper.selectById(id);
        if (sourceDO == null) {
            throw new ServiceException("该数据源不存在");
        }
        if (alertEventService.exists(new LambdaQueryWrapper<AlertEventDO>().eq(AlertEventDO::getSystemCode, sourceDO.getSystemCode()))) {
            throw new ServiceException("该数据源存在引用预警事件，不支持删除");
        }
        return SqlHelper.retBool(dataSourceMapper.deleteById(id));
    }

    @Override
    public Boolean existsSystemCode(String systemCode) {
        return dataSourceMapper.exists(new LambdaQueryWrapper<DataSourceDO>().eq(DataSourceDO::getSystemCode, systemCode));
    }

    @Override
    public Set<String> getAllSystemName() {
        List<DataSourceDO> dataSourceDoS = dataSourceMapper.selectList(new LambdaQueryWrapper<DataSourceDO>().select(DataSourceDO::getSystemName));
        return dataSourceDoS.stream().map(DataSourceDO::getSystemName).collect(Collectors.toSet());
    }

    @Override
    public Boolean updateInfo(DataSourceUpdateBO updateBO){
        DataSourceDO entity = new DataSourceDO();
        BeanUtils.copyProperties(updateBO, entity);
        return SqlHelper.retBool(dataSourceMapper.updateById(entity));
    }


    @Override
    public IPage<DataSourcePageVO> getPageInfo(DataSourceQueryParamsBO queryParamsBO){
        if (StringUtils.isBlank(queryParamsBO.getOrderFields())) {
            queryParamsBO.setOrderFields("createTime");
        }
        if (StringUtils.isBlank(queryParamsBO.getOrderRules())) {
            queryParamsBO.setOrderRules("desc");
        }
        return dataSourceMapper.selectPage(queryParamsBO.pageInfo(), queryParamsBO.queryWrapper()).convert(DataSourcePageVO::of);
    }

}
