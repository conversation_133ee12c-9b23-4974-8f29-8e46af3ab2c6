package com.tool.converge.business.system;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tool.converge.repository.domain.system.db.DingTalkUserDO;
import com.tool.converge.repository.domain.system.bo.DingTalkUserSaveBO;
import com.tool.converge.repository.domain.system.bo.DingTalkUserUpdateBO;
import com.tool.converge.repository.domain.system.bo.DingTalkUserQueryParamsBO;
import com.tool.converge.repository.domain.system.vo.DingTalkUserDetailVO;
import com.tool.converge.repository.domain.system.vo.DingTalkUserPageVO;

/**
 * <p>
 * 钉钉用户 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-07 08:48:38
 */
public interface DingTalkUserService extends IService<DingTalkUserDO> {

    /**
     * 添加信息
     *
     * @param saveBO
     * @return
     */
    Boolean saveInfo(DingTalkUserSaveBO saveBO);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    Boolean delInfo(Long id);

    /**
     * 修改信息
     *
     * @param updateBO
     * @return
     */
    Boolean updateInfo(DingTalkUserUpdateBO updateBO);

    /**
     * 通过ID获取信息
     *
     * @param id
     * @return
     */
    DingTalkUserDetailVO getInfo(Long id);

    /**
     * 分页获取列表
     *
     * @param queryParamsBO
     * @return
     */
    IPage<DingTalkUserPageVO> getPageInfo(DingTalkUserQueryParamsBO queryParamsBO);

    /**
     * 同步钉钉部门和用户
     *
     * @return
     */
    void sync();
}
