package com.tool.converge.business.system;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tool.converge.repository.domain.system.bo.SysDictValueQueryParamsBO;
import com.tool.converge.repository.domain.system.bo.SysDictValueSaveBO;
import com.tool.converge.repository.domain.system.bo.SysDictValueUpdateBO;
import com.tool.converge.repository.domain.system.db.SysDictValueDO;
import com.tool.converge.repository.domain.system.vo.SysDictValuePageVO;

import java.util.List;

/**
 * <p>
 * 字典配置值 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-04
 */
public interface SysDictValueService extends IService<SysDictValueDO> {

    /**
     * 字典配置值分页查询
     *
     * @param sysDictValueQueryParamsBO 字典配置值信息
     * @return
     */
    IPage<SysDictValuePageVO> selectPage(SysDictValueQueryParamsBO sysDictValueQueryParamsBO);

    /**
     * 添加信息
     *
     * @param saveBO
     * @return
     */
    Boolean saveInfo(SysDictValueSaveBO saveBO);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    Boolean delInfo(Long id);

    /**
     * 修改信息
     *
     * @param updateBO
     * @return
     */
    Boolean updateInfo(SysDictValueUpdateBO updateBO);

    /**
     * 根据keyId查询字典配置值
     * @param keyName
     * @return
     */
    List<SysDictValueDO> listByKeyName(String keyName);
}
