package com.tool.converge.business.system.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.tool.converge.business.system.DingTalkDeptService;
import com.tool.converge.repository.domain.system.bo.DingTalkDeptQueryParamsBO;
import com.tool.converge.repository.domain.system.bo.DingTalkDeptSaveBO;
import com.tool.converge.repository.domain.system.bo.DingTalkDeptUpdateBO;
import com.tool.converge.repository.domain.system.db.DingTalkDeptDO;
import com.tool.converge.repository.domain.system.vo.DingTalkDeptDetailVO;
import com.tool.converge.repository.domain.system.vo.DingTalkDeptPageVO;
import com.tool.converge.repository.mapper.system.DingTalkDeptMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 钉钉部门 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-07 08:48:38
 */
@Slf4j
@Service
public class DingTalkDeptServiceImpl extends ServiceImpl<DingTalkDeptMapper, DingTalkDeptDO> implements DingTalkDeptService {

    @Resource
    private DingTalkDeptMapper dingTalkDeptMapper;

    @Override
    public Boolean saveInfo(DingTalkDeptSaveBO saveBO) {
        DingTalkDeptDO entity = new DingTalkDeptDO();
        BeanUtils.copyProperties(saveBO, entity);
        return SqlHelper.retBool(dingTalkDeptMapper.insert(entity));
    }

    @Override
    public Boolean delInfo(Long id) {
        return SqlHelper.retBool(dingTalkDeptMapper.deleteById(id));
    }

    @Override
    public Boolean updateInfo(DingTalkDeptUpdateBO updateBO) {
        DingTalkDeptDO entity = new DingTalkDeptDO();
        BeanUtils.copyProperties(updateBO, entity);
        return SqlHelper.retBool(dingTalkDeptMapper.updateById(entity));
    }

    @Override
    public DingTalkDeptDetailVO getInfo(Long id) {
        DingTalkDeptDO entity = dingTalkDeptMapper.selectById(id);
        return DingTalkDeptDetailVO.of(entity);
    }

    @Override
    public IPage<DingTalkDeptPageVO> getPageInfo(DingTalkDeptQueryParamsBO queryParamsBO) {
        return dingTalkDeptMapper.selectPage(queryParamsBO.pageInfo(), queryParamsBO.queryWrapper()).convert(DingTalkDeptPageVO::of);
    }

    @Override
    public List<DingTalkDeptDetailVO> allDept() {
        return Collections.emptyList();
    }

}
