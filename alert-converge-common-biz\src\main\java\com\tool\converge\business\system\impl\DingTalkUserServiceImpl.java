package com.tool.converge.business.system.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.dingtalk.api.response.OapiV2DepartmentListsubResponse;
import com.dingtalk.api.response.OapiV2UserListResponse;
import com.tool.converge.business.dingtalk.DingTalkApiService;
import com.tool.converge.business.system.DingTalkUserService;
import com.tool.converge.repository.domain.system.bo.DingTalkUserQueryParamsBO;
import com.tool.converge.repository.domain.system.bo.DingTalkUserSaveBO;
import com.tool.converge.repository.domain.system.bo.DingTalkUserUpdateBO;
import com.tool.converge.repository.domain.system.db.DingTalkDeptDO;
import com.tool.converge.repository.domain.system.db.DingTalkUserDO;
import com.tool.converge.repository.domain.system.vo.DingTalkUserDetailVO;
import com.tool.converge.repository.domain.system.vo.DingTalkUserPageVO;
import com.tool.converge.repository.mapper.system.DingTalkDeptMapper;
import com.tool.converge.repository.mapper.system.DingTalkUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.util.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 钉钉用户 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-07 08:48:38
 */
@Slf4j
@Service
public class DingTalkUserServiceImpl extends ServiceImpl<DingTalkUserMapper, DingTalkUserDO> implements DingTalkUserService {

    @Resource
    private DingTalkUserMapper dingTalkUserMapper;

    @Resource
    private DingTalkApiService dingTalkApiService;

    @Resource
    private DingTalkDeptMapper dingTalkDeptMapper;

    @Override
    public Boolean saveInfo(DingTalkUserSaveBO saveBO) {
        DingTalkUserDO entity = new DingTalkUserDO();
        BeanUtils.copyProperties(saveBO, entity);
        return SqlHelper.retBool(dingTalkUserMapper.insert(entity));
    }

    @Override
    public Boolean delInfo(Long id) {
        return SqlHelper.retBool(dingTalkUserMapper.deleteById(id));
    }

    @Override
    public Boolean updateInfo(DingTalkUserUpdateBO updateBO) {
        DingTalkUserDO entity = new DingTalkUserDO();
        BeanUtils.copyProperties(updateBO, entity);
        return SqlHelper.retBool(dingTalkUserMapper.updateById(entity));
    }

    @Override
    public DingTalkUserDetailVO getInfo(Long id) {
        DingTalkUserDO entity = dingTalkUserMapper.selectById(id);
        return DingTalkUserDetailVO.of(entity);
    }

    @Override
    public IPage<DingTalkUserPageVO> getPageInfo(DingTalkUserQueryParamsBO queryParamsBO) {
        return dingTalkUserMapper.selectPage(queryParamsBO.pageInfo(), queryParamsBO.queryWrapper()).convert(DingTalkUserPageVO::of);
    }

    @Override
    public void sync() {
        List<OapiV2DepartmentListsubResponse.DeptBaseResponse> departmentList = dingTalkApiService.getDepartmentList();
        List<DingTalkDeptDO> list = departmentList.stream().map(item -> {
            DingTalkDeptDO deptDO = new DingTalkDeptDO();
            deptDO.setParentId(item.getParentId());
            deptDO.setDeptId(item.getDeptId());
            deptDO.setDeptName(item.getName());
            return deptDO;
        }).collect(Collectors.toList());
        List<Long> deptIdList = list.stream().map(DingTalkDeptDO::getDeptId).collect(Collectors.toList());
        // 先删除不存在的部门
        dingTalkDeptMapper.delete(new LambdaQueryWrapper<DingTalkDeptDO>()
                .notIn(!list.isEmpty(), DingTalkDeptDO::getDeptId, deptIdList));
        if (!list.isEmpty()) {
            // 插入或更新部门
            dingTalkDeptMapper.insertOrUpdateByDeptId(list);
        }
        List<DingTalkUserDO> userList = new ArrayList<>();
        departmentList.forEach(dept -> {
            List<OapiV2UserListResponse.ListUserResponse> dtUserList = dingTalkApiService.getUserList(dept.getDeptId());
            userList.addAll(dtUserList.stream().map(user -> {
                DingTalkUserDO dingTalkUserDO = new DingTalkUserDO();
                dingTalkUserDO.setDeptId(dept.getDeptId());
                dingTalkUserDO.setDeptName(dept.getName());
                dingTalkUserDO.setPost(user.getTitle());
                dingTalkUserDO.setMobile(user.getMobile());
                dingTalkUserDO.setEmail(user.getEmail());
                dingTalkUserDO.setUserId(user.getUserid());
                dingTalkUserDO.setName(user.getName());
                return dingTalkUserDO;
            }).collect(Collectors.toList()));
        });
        List<Pair<Long, String>> deptIdAndUserIdList = userList.stream().map(item -> Pair.create(item.getDeptId(), item.getUserId())).collect(Collectors.toList());
        // 先删除不存在的部门用户
        dingTalkUserMapper.deleteByDeptIdAndUserId(deptIdAndUserIdList);
        if (!userList.isEmpty()) {
            // 插入或更新部门用户
            dingTalkUserMapper.insertOrUpdateByDeptIdAndUserId(userList);
        }
    }

}
