package com.tool.converge.business.system.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tool.converge.business.system.SysUserService;
import com.tool.converge.repository.domain.system.db.SysUserDO;
import com.tool.converge.repository.mapper.system.SysUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 人员 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-06
 */
@Slf4j
@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUserDO> implements SysUserService {

    @Resource
    private SysUserMapper sysUserMapper;

}