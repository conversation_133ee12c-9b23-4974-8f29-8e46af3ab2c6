package com.tool.converge.common.annotation;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
 /**
  * <AUTHOR>
  * @date 2025/3/11 9:11
  * @Description excel表头注解
  * @MethodName
  */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ExcelHeader {

    /*
     * 表头名
     */
    String name();
}