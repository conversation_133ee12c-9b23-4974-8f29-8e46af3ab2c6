package com.tool.converge.common.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 异步线程配置
 *
 * @param TODO
 * <AUTHOR>
 * @date 2025/3/12 15:37
 * @Description TODO
 * @MethodName
 */
@Configuration
@EnableAsync
public class AsyncThreadConfig {

    @Bean(name = "Alert-Event-Submit")
    public TaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数（CPU核心数 × 2 或 根据任务类型调整）
        executor.setCorePoolSize(Runtime.getRuntime().availableProcessors() * 2);
        // 最大线程数（核心线程数 × 2 或 根据业务峰值调整）
        executor.setMaxPoolSize(executor.getCorePoolSize() * 2);
        // 任务队列容量（根据预期任务量设置，如 1000）
        executor.setQueueCapacity(1000);
        // 线程空闲超时时间（单位：秒）
        executor.setKeepAliveSeconds(60);
        // 是否允许核心线程超时回收（根据任务频率决定）
        executor.setAllowCoreThreadTimeOut(false);
        // 拒绝策略（默认AbortPolicy，可改为CallerRunsPolicy）
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setThreadNamePrefix("Alert-Event-Submit-");
        executor.initialize(); // 初始化线程池
        return executor;
    }

}
