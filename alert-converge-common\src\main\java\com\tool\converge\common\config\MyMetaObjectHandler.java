package com.tool.converge.common.config;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzed.sso.client.utils.SSOShiroUtils;
import com.hzed.sso.core.model.SSOUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description: 自动填充处理器
 * @date 2023/1/18 9:25
 */
@Slf4j
@Component
@ConditionalOnClass(BaseMapper.class)
public class MyMetaObjectHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        SSOUser currentUser = null;
        try {
            currentUser = SSOShiroUtils.getCurrentUser();
        } catch (Exception e) {
            log.warn("insertFill获取用户异常");
        }
        LocalDateTime now = LocalDateTime.now();
        this.strictInsertFill(metaObject, "updateTime", LocalDateTime.class, now);
        this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, now);
        this.strictInsertFill(metaObject, "deleted", Boolean.class, false);
        this.strictInsertFill(metaObject, "creator", String.class, ObjectUtil.isNotNull(currentUser) ? currentUser.getUserName() : null);
        this.strictInsertFill(metaObject, "updater", String.class, ObjectUtil.isNotNull(currentUser) ? currentUser.getUserName() : null);
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        SSOUser currentUser = null;
        try {
            currentUser = SSOShiroUtils.getCurrentUser();
        } catch (Exception e) {
            log.warn("updateFill获取用户异常");
        }
        this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
        this.strictUpdateFill(metaObject, "updater", String.class, ObjectUtil.isNotNull(currentUser) ? currentUser.getUserName() : null);
    }
}
