# 存放【生产和测试环境】通用配置
server:
  shutdown: graceful
  servlet:
    encoding:
      charset: UTF-8
      enabled: true
      force: true

spring:
  lifecycle:
    timeout-per-shutdown-phase: 85s
  main:
    allow-bean-definition-overriding: true
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

# MyBatisPlus
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*Mapper.xml
  global-config:
    banner: false
    db-config:
      id-type: ASSIGN_ID

# 组件配置
pub:
  tool:
    error-mess-handler-enabled: true
    long-to-string-enabled: true
    java-time-module-enabled: true
    http-error-json-handler-enabled: true

# prometheus
management:
  metrics:
    tags:
      application: ${spring.application.name}
    mongo:
      connectionpool:
        enabled: false
      command:
        enabled: false
  endpoints:
    web:
      exposure:
        include: health,metrics,prometheus
  endpoint:
    health:
      show-details: always

hzed:
  sso:
    systemDomainCode: warning-admin
    sso-service-url: https://sso.qmwallet.vip/sso-service
    access-denied-handle-type: json
    filter-chain-definition-map[/alertEvent/submit]: anon