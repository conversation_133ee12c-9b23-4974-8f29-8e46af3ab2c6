package com.tool.converge.job;

import cn.hutool.core.net.NetUtil;
import com.tool.converge.common.constant.CommonConstant;
import com.hzed.structure.lock.annotation.EnableLock;
import com.hzed.structure.log.annotation.EnableLog;
import com.alicp.jetcache.anno.config.EnableMethodCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.ConfigurableEnvironment;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2021/7/22
 **/
@EnableLock
@EnableLog
@Slf4j
@EnableMethodCache(basePackages = CommonConstant.BASE_PACKAGE)
@SpringBootApplication(scanBasePackages = CommonConstant.BASE_PACKAGE)
public class JobApplication {


    public static void main(String[] args) {
        System.setProperty("spring.devtools.restart.enabled", "false");
        SpringApplication application = new SpringApplication(JobApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        ConfigurableApplicationContext context = application.run(args);
        ConfigurableEnvironment env = context.getEnvironment();
        String ip = NetUtil.getIpByHost(NetUtil.getLocalHostName());
        String name = env.getProperty("spring.application.name");
        String port = env.getProperty("server.port");
        String path = env.getProperty("server.servlet.context-path");
        String active = env.getProperty("spring.profiles.active");
        log.info("\n--------------------------------------------------------------------------\n\t" +
                "Application " + name + " is running, Active " + active + ".\n\t" +
                "Local: \t\thttp://localhost:" + port + path + "/\n\t" +
                "External: \thttp://" + ip + ":" + port + path + "/\n\t" +
                "Health: \thttp://" + ip + ":" + port + path + "/actuator/health\n\t" +
                "Swagger: \thttp://" + ip + ":" + port + path + "/doc.html\n" +
                "--------------------------------------------------------------------------");
    }
}
