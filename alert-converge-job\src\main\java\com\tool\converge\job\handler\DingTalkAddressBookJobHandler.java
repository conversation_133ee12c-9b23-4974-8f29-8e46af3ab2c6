package com.tool.converge.job.handler;

import com.hzed.structure.log.annotation.TraceId;
import com.hzed.structure.log.util.MdcUtil;
import com.tool.converge.business.system.DingTalkUserService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

/**
 * <AUTHOR>
 * @description 钉钉通讯录定时任务
 * @date 2025/8/7 10:35
 */
@Slf4j
@Component
public class DingTalkAddressBookJobHandler {

    @Autowired
    private DingTalkUserService dingTalkUserService;


    @XxlJob("syncAddressBookJobHandler")
    @TraceId("钉钉通讯录同步")
    public ReturnT<String> syncAddressBookJobHandler(String param) {
        try {
            XxlJobLogger.log("开始执行钉钉通讯录同步定时任务... traceId:{}", MdcUtil.getTrace());
            log.info("开始执行钉钉通讯录同步定时任务... ");
            StopWatch sw = new StopWatch();
            sw.start();
            dingTalkUserService.sync();
            sw.stop();
            XxlJobLogger.log("执行钉钉通讯录同步定时任务结束,耗时:{}", sw.getTotalTimeMillis());
            log.info("执行钉钉通讯录同步定时任务结束,耗时:{}", sw.getTotalTimeMillis());
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            XxlJobLogger.log(e);
            log.error("执行钉钉通讯录同步定时任务异常", e);
            return ReturnT.FAIL;
        }
    }
}
