package com.tool.converge.job.handler;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.hzed.structure.log.annotation.TraceId;
import com.hzed.structure.log.util.MdcUtil;
import com.tool.converge.business.capital.CapitalService;
import com.tool.converge.common.constant.CommonConstant;
import com.tool.converge.repository.domain.capital.db.CapitalDO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025-08-06 10:32
 */
@Slf4j
@Component
public class SyncCapitalHandler {

    @Resource
    private RestTemplate restTemplate;
    @Resource
    private CapitalService capitalService;

    @Value("${capital-service.url}")
    private String capitalServiceUrl;
    /**
     * 资方列表接口地址
     */
    private String syncCapitalListUrl =  "/qmqb/ledger1/api/capital-service/v1/public/capital/search";

    public static final String CODE = "code";
    public static final String MSG = "msg";
    public static final String RESULT = "result";
    public static final String RECOREDS = "records";

    @TraceId("同步资方信息")
    @XxlJob("syncCapitalHandler")
    public ReturnT<String> syncCapitalHandler(String param) throws Exception {
        XxlJobLogger.log("开始同步资方信息,traceId:{}", MdcUtil.getTrace());
        log.info("开始同步资方信息");
        try {
            // 1. 调用接口获取资方信息
            String url = capitalServiceUrl + syncCapitalListUrl;
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.add("source", "alert-converge");
            // 构造请求体
            Map<String, Object> requestBody = new HashMap<>(2);
            requestBody.put("pageNum", 1);
            requestBody.put("pageSize", Integer.MAX_VALUE);
            requestBody.put("client", CommonConstant.CommonValStr.ELEVEN);
            requestBody.put("requestNo", System.currentTimeMillis() + "" + ThreadLocalRandom.current().nextInt(1000));

            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);
            // 发送请求
            ResponseEntity<Map> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    Map.class);
            // 2. 处理响应数据
            if (response.getStatusCode() == HttpStatus.OK) {
                Map<String, Object> responseBody = response.getBody();
                if (CommonConstant.CommonValStr.THOUSAND.equals(responseBody.get(CODE))) {
                    Map<String, Object> result = (Map<String, Object>) responseBody.get(RESULT);
                    List<Map<String, Object>> records = (List<Map<String, Object>>) result.get(RECOREDS);
                    if (CollectionUtils.isNotEmpty(records)) {
                        // 3. 同步到数据库
                        syncCapitalRecord(records);
                    }
                } else {
                    XxlJobLogger.log("接口返回错误: code={}, msg={}", responseBody.get(CODE), responseBody.get(MSG));
                    log.error("接口返回错误: code={}, msg={}", responseBody.get(CODE), responseBody.get(MSG));
                    return ReturnT.FAIL;
                }
            } else {
                XxlJobLogger.log("接口调用失败, 状态码: {}", response.getStatusCodeValue());
                log.error("接口调用失败, 状态码: {}", response.getStatusCodeValue());
                return ReturnT.FAIL;
            }
        } catch (Exception e) {
            XxlJobLogger.log("同步资方信息异常: {}", e.getMessage());
            log.error("同步资方信息异常", e);
            return ReturnT.FAIL;
        }
        XxlJobLogger.log("同步资方信息完成,traceId:{}", MdcUtil.getTrace());
        log.info("同步资方信息完成");
        return ReturnT.SUCCESS;
    }

    /**
     * 同步单条资方记录到数据库
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncCapitalRecord(List<Map<String, Object>> records) {
        try {
            // 1. 转换新数据
            List<CapitalDO> newCapitalList = records.stream().map(this::convertToCapitalDO).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(newCapitalList)) {
                log.warn("同步资方信息: 数据转换后无有效记录");
                return;
            }
            // 2. 获取现有数据
            List<CapitalDO> oldCapitalList = capitalService.list();
            Map<Long, CapitalDO> oldCapitalMap = oldCapitalList.stream().collect(Collectors.toMap(CapitalDO::getId, Function.identity()));

            // 3. 分类处理数据
            List<CapitalDO> capitalToInsert = new ArrayList<>();
            List<CapitalDO> capitalToUpdate = new ArrayList<>();
            List<Long> capitalToDelete = new ArrayList<>();

            // 3.1 找出需要删除的记录(本地有但接口没有的)
            oldCapitalList.stream()
                    .filter(old -> newCapitalList.stream().noneMatch(n -> n.getId().equals(old.getId())))
                    .forEach(old -> capitalToDelete.add(old.getId()));

            // 3.2 分类插入和更新记录
            for (CapitalDO newCapital : newCapitalList) {
                CapitalDO oldCapital = oldCapitalMap.get(newCapital.getId());
                if (oldCapital == null) {
                    capitalToInsert.add(newCapital);
                } else if (!isSameCapital(oldCapital, newCapital)) {
                    capitalToUpdate.add(newCapital);
                }
            }
            // 4. 执行批量操作
            if (!capitalToDelete.isEmpty()) {
                capitalService.physicalDeleteBatchIds(capitalToDelete);
            }
            if (!capitalToInsert.isEmpty()) {
                capitalService.saveBatch(capitalToInsert);
            }
            if (!capitalToUpdate.isEmpty()) {
                capitalService.updateBatchById(capitalToUpdate);
            }

        } catch (Exception e) {
            log.error("同步资方记录异常", e);
            throw new RuntimeException("同步资方记录失败", e);
        }
    }

    /**
     * 比较两个资方记录是否相同(比较关键字段)
     */
    private boolean isSameCapital(CapitalDO oldCapital, CapitalDO newCapital) {
        return Objects.equals(oldCapital.getCapitalName(), newCapital.getCapitalName())
                && Objects.equals(oldCapital.getCapitalCode(), newCapital.getCapitalCode());
    }


    /**
     * 将接口数据转换为CapitalDO对象
     */
    private CapitalDO convertToCapitalDO(Map<String, Object> record) {
        return CapitalDO.builder()
                .id(Long.valueOf(String.valueOf(record.get("id"))))
                .capitalName(String.valueOf(record.get("capitalName")))
                .capitalCode(String.valueOf(record.get("capitalCode")))
                .build();
    }

}
