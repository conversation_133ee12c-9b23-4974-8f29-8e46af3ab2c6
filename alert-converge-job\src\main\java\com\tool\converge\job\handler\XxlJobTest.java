package com.tool.converge.job.handler;

import com.hzed.structure.log.annotation.TraceId;
import com.hzed.structure.log.util.MdcUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * XXL-JOB示例
 *
 * <AUTHOR>
 * @date 2021/7/22
 */
@Slf4j
@Component
public class XxlJobTest {
    /**
     * 1、简单任务示例（Bean模式）
     */
    @TraceId("测试demo")
    @XxlJob("demoJobHandler")
    public ReturnT<String> demoJobHandler(String param) throws Exception {
        XxlJobLogger.log("XXL-JOB, Hello World. traceId:{}", MdcUtil.getTrace());
        log.info("XXL-JOB, Hello World.");
        return ReturnT.SUCCESS;
    }
}
