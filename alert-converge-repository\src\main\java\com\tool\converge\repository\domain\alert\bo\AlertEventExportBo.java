package com.tool.converge.repository.domain.alert.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>
 * 导出预警事件
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10 17:09:07
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "AlertEventExportBO对象", description = "导出预警事件")
public class AlertEventExportBo {

    @Schema(description = "预警事件主键id")
    @NotNull(message = "预警事件id不能为空")
    private List<Long> ids;


    @Schema(description = "排序字段名")
    private List<String> orderByColumns;

}
