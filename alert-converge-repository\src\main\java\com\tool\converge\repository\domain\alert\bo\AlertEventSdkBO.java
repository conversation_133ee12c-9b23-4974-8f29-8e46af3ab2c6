package com.tool.converge.repository.domain.alert.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <p>
 * 预警事件
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10 14:04:53
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "AlertEventSaveBO对象", description = "预警事件")
public class AlertEventSdkBO implements Serializable{

    private static final long serialVersionUID = 1L;

    @Schema(description = "事件编号")
    @NotBlank(message = "事件编号")
    private String eventId;

    @Schema(description = "系统编号")
    @NotBlank(message = "系统编号不能为空")
    private String systemId;


    @Schema(description = "模型编号")
    @NotBlank(message = "模型编号不能为空")
    private String modelId;


    @Schema(description = "第三方名称")
    @NotBlank(message = "第三方名称不能为空")
    private String platformName;


    @Schema(description = "期次")
    private String period;


    @Schema(description = "原因")
    @NotBlank(message = "原因不能为空")
    private String reason;

    @Schema(description = "md5唯一标识不能为空")
    @NotBlank(message = "md5唯一标识不能为空")
    private String md5;


    @Schema(description = "业务唯一编号")
    @NotBlank(message = "业务唯一编号不能为空")
    private String serviceNo;


    @Schema(description = "指标值")
    @NotBlank(message = "指标值编号不能为空")
    private String indexValue;


    @Schema(description = "拓展字段")
    private String payload;


    @Schema(description = "状态值， 例如：单状态/还款状态/ 支付状态/推送状态")
    @NotBlank(message = "状态值不能为空")
    private String state;


}
