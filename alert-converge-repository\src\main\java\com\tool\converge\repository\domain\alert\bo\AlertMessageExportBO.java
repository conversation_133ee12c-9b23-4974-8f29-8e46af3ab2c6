package com.tool.converge.repository.domain.alert.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>
 * 导出预警消息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "AlertMessageExportBO对象", description = "导出预警消息")
public class AlertMessageExportBO {

    @Schema(description = "预警消息主键id")
    @NotNull(message = "预警消息id不能为空")
    private List<Long> ids;

    @Schema(description = "排序字段名")
    private List<String> orderByColumns;

}
