package com.tool.converge.repository.domain.alert.bo;

import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05 14:43:46
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "AlertModelConfigUpdateBO对象", description = "")
public class AlertModelConfigUpdateBO implements Serializable{

    private static final long serialVersionUID = 1L;


    @Schema(description = "预警配置id")
    private Long id;

    @Schema(description = "模型ID")
    private Long modelId;

    @Schema(description = "预警频率 0为不限制")
    private Integer frequency;

    @Schema(description = "预警方式 1:钉钉，2:短信，3:钉钉+短信")
    private Integer warnType;

    @Schema(description = "预警内容")
    private String warnContent;

    @Schema(description = "钉钉群地址")
    private String webhook;

}
