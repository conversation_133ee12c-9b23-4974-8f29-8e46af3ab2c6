package com.tool.converge.repository.domain.alert.bo;

import java.io.Serializable;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tool.converge.repository.domain.common.PageParamsBO;
import com.tool.converge.repository.domain.alert.db.AlertModelNotifiersDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05 10:41:50
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "AlertModelNotifiersQueryParamsBO对象", description = "")
public class AlertModelNotifiersQueryParamsBO extends PageParamsBO<AlertModelNotifiersDO> implements Serializable{

    private static final long serialVersionUID = 1L;


    @Schema(description = "通知用户ID")
    private Long userId;

    @Schema(description = "预警配置ID")
    private Long configId;

    @Override
    public LambdaQueryWrapper<AlertModelNotifiersDO> queryWrapper() {

        LambdaQueryWrapper<AlertModelNotifiersDO> query = new LambdaQueryWrapper<>();

        query.eq(userId!=null,AlertModelNotifiersDO::getUserId,userId);

        query.eq(configId!=null,AlertModelNotifiersDO::getConfigId,configId);

        return query;
    }
}
