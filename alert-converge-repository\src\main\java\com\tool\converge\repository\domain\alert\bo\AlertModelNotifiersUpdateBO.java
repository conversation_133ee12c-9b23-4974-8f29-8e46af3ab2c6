package com.tool.converge.repository.domain.alert.bo;

import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05 10:41:50
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "AlertModelNotifiersUpdateBO对象", description = "")
public class AlertModelNotifiersUpdateBO implements Serializable{

    private static final long serialVersionUID = 1L;


    @Schema(description = "通知用户ID")
    private Long userId;

    @Schema(description = "预警配置ID")
    private Long configId;

}
