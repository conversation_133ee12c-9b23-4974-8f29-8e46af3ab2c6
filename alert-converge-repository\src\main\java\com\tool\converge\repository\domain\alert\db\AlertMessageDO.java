package com.tool.converge.repository.domain.alert.db;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 预警消息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_alert_message")
@Schema(name = "AlertMessageDO对象", description = "预警消息")
public class AlertMessageDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @TableId("`id`")
    private Long id;

    @Schema(description = "预警事件编号，SDK生成")
    @TableField("`event_id`")
    private String eventId;

    @Schema(description = "模型编码")
    @TableField("`model_code`")
    private String modelCode;

    @Schema(description = "预警名称")
    @TableField("`model_name`")
    private String modelName;

    @Schema(description = "预警类型")
    @TableField("`alert_type`")
    private String alertType;

    @Schema(description = "第三方名称")
    @TableField("`platform_name`")
    private String platformName;

    @Schema(description = "期次")
    @TableField("`period`")
    private String period;

    @Schema(description = "业务唯一编号")
    @TableField("`service_no`")
    private String serviceNo;

    @Schema(description = "业务类型")
    @TableField("`business_type`")
    private String businessType;

    @Schema(description = "状态值， 例如：单状态/还款状态/ 支付状态/推送状态")
    @TableField("`state`")
    private String state;

    @Schema(description = "指标值")
    @TableField("`index_value`")
    private String indexValue;

    @Schema(description = "原因")
    @TableField("`reason`")
    private String reason;

    @Schema(description = "拓展字段")
    @TableField("`payload`")
    private String payload;

    @Schema(description = "规则id")
    @TableField("`rule_id`")
    private Long ruleId;

    @Schema(description = "预警规则")
    @TableField("`rule_name`")
    private String ruleName;

    @Schema(description = "是否关联规则 0为不关联，1为关联")
    @TableField("`related`")
    private Boolean related;

    @Schema(description = "预警级别")
    @TableField("`warn_level`")
    private String warnLevel;

    @Schema(description = "系统编号")
    @TableField("`system_code`")
    private String systemCode;

    @Schema(description = "系统名称")
    @TableField("`system_name`")
    private String systemName;

    @Schema(description = "预警事件时间/上报时间")
    @TableField("`alert_time`")
    private LocalDateTime alertTime;

    @Schema(description = "预警配置id")
    @TableField("`config_id`")
    private Long configId;

    @Schema(description = "预警频率 0为不限制")
    @TableField("`frequency`")
    private Integer frequency;

    @Schema(description = "通知方式 1:钉钉，2:短信，3:钉钉+短信")
    @TableField("`warn_type`")
    private Integer warnType;

    @Schema(description = "通知人员")
    @TableField("`notification_users`")
    private String notificationUsers;

    @Schema(description = "创建时间")
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    @TableField(value = "`update_time`", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    @TableField(value = "`creator`", fill = FieldFill.INSERT)
    private String creator;

    @Schema(description = "更新人")
    @TableField(value = "`updater`", fill = FieldFill.INSERT_UPDATE)
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    @TableField(value = "`deleted`", fill = FieldFill.INSERT)
    @TableLogic
    private Boolean deleted;

    @Schema(description = "开始时间")
    @TableField(select = false)
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @TableField(select = false)
    private LocalDateTime endTime;
}
