package com.tool.converge.repository.domain.alert.db;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05 14:43:46
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_alert_model_config")
@Schema(name = "AlertModelConfigDO对象", description = "")
public class AlertModelConfigDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "预警配置id")
    @TableId("`id`")
    private Long id;

    @Schema(description = "模型ID")
    @TableField("`model_id`")
    private Long modelId;

    @Schema(description = "预警频率 0为不限制")
    @TableField("`frequency`")
    private Integer frequency;

    @Schema(description = "预警方式 1:钉钉，2:短信，3:钉钉+短信")
    @TableField("`warn_type`")
    private Integer warnType;

    @Schema(description = "预警内容")
    @TableField("`warn_content`")
    private String warnContent;

    @Schema(description = "钉钉群地址")
    @TableField("`webhook`")
    private String webhook;

    @Schema(description = "创建时间")
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    @TableField(value = "`update_time`", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    @TableField(value = "`creator`", fill = FieldFill.INSERT)
    private String creator;

    @Schema(description = "更新人")
    @TableField(value = "`updater`", fill = FieldFill.INSERT_UPDATE)
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    @TableField(value = "`deleted`", fill = FieldFill.INSERT)
    @TableLogic
    private Boolean deleted;


}
