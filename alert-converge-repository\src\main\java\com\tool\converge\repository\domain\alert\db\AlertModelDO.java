package com.tool.converge.repository.domain.alert.db;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 预警模型
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-06 14:45:26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_alert_model")
@Schema(name = "AlertModelDO对象", description = "预警模型")
public class AlertModelDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "模型id")
    @TableId("`id`")
    private Long id;

    @Schema(description = "预警名称")
    @TableField("`model_name`")
    private String modelName;

    @Schema(description = "模型编码")
    @TableField("`model_code`")
    private String modelCode;

    @Schema(description = "业务类型")
    @TableField("`business_type`")
    private String businessType;

    @Schema(description = "预警类型")
    @TableField("`alert_type`")
    private String alertType;

    @Schema(description = "是否关联规则 0为不关联，1为关联")
    @TableField("`related`")
    private Boolean related;

    @Schema(description = "是否生成预警消息 0为不生成，1为生成")
    @TableField("`warned`")
    private Boolean warned;

    @Schema(description = "描述")
    @TableField("`description`")
    private String description;

    @Schema(description = "创建时间")
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    @TableField(value = "`update_time`", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    @TableField(value = "`creator`", fill = FieldFill.INSERT)
    private String creator;

    @Schema(description = "更新人")
    @TableField(value = "`updater`", fill = FieldFill.INSERT_UPDATE)
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    @TableField(value = "`deleted`", fill = FieldFill.INSERT)
    @TableLogic
    private Boolean deleted;


}
