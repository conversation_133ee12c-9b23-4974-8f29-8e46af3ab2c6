package com.tool.converge.repository.domain.alert.db;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05 10:41:50
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_alert_model_notifiers")
@Schema(name = "AlertModelNotifiersDO对象", description = "")
public class AlertModelNotifiersDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "通知用户ID")
    private Long userId;

    @Schema(description = "预警配置ID")
    private Long configId;


}
