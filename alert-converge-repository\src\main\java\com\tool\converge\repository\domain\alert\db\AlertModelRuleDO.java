package com.tool.converge.repository.domain.alert.db;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 模型规则关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-06 14:45:26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_alert_model_rule")
@Schema(name = "AlertModelRuleDO对象", description = "模型规则关联表")
public class AlertModelRuleDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "规则ID")
    private Long ruleId;

    @Schema(description = "模型ID")
    private Long modelId;


}
