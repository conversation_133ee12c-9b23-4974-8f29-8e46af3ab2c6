package com.tool.converge.repository.domain.alert.vo;

import com.tool.converge.common.annotation.ExcelHeader;
import com.tool.converge.repository.domain.alert.db.AlertMessageDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <p>
 * 预警消息导出
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "AlertMessageExportVO对象", description = "预警消息导出")
public class AlertMessageExportVO implements Serializable{

    private static final long serialVersionUID = 1L;

    @Schema(description = "预警事件编号")
    @ExcelHeader(name = "预警事件编号")
    private String eventId;

    @Schema(description = "模型编码")
    @ExcelHeader(name = "模型编号")
    private String modelCode;

    @Schema(description = "预警名称")
    @ExcelHeader(name = "预警名称")
    private String modelName;

    @Schema(description = "预警类型")
    @ExcelHeader(name = "预警类型")
    private String alertType;

    @Schema(description = "第三方名称")
    @ExcelHeader(name = "第三方名称")
    private String platformName;

    @Schema(description = "期次")
    @ExcelHeader(name = "期次")
    private String period;

    @Schema(description = "业务唯一编号")
    @ExcelHeader(name = "业务唯一编号")
    private String serviceNo;

    @Schema(description = "业务类型")
    @ExcelHeader(name = "业务类型")
    private String businessType;

    @Schema(description = "状态值")
    @ExcelHeader(name = "状态值")
    private String state;

    @Schema(description = "指标值")
    @ExcelHeader(name = "指标值")
    private String indexValue;

    @Schema(description = "原因")
    @ExcelHeader(name = "原因")
    private String reason;

    @Schema(description = "拓展字段")
    @ExcelHeader(name = "拓展")
    private String payload;

    @Schema(description = "预警规则")
    @ExcelHeader(name = "预警规则")
    private String ruleName;

    @Schema(description = "是否关联规则")
    @ExcelHeader(name = "是否关联规则")
    private String relatedText;

    @Schema(description = "预警级别")
    @ExcelHeader(name = "预警级别")
    private String warnLevel;

    @Schema(description = "系统名称")
    @ExcelHeader(name = "系统名称")
    private String systemName;

    @Schema(description = "预警事件时间")
    @ExcelHeader(name = "预警事件时间")
    private LocalDateTime alertTime;

    @Schema(description = "预警频率")
    @ExcelHeader(name = "预警频率")
    private String frequencyText;

    @Schema(description = "通知方式")
    @ExcelHeader(name = "通知方式")
    private String warnTypeText;

    @Schema(description = "通知人员")
    @ExcelHeader(name = "通知人员")
    private String notificationUsers;

    @Schema(description = "是否关联规则")
    private Boolean related;


    @Schema(description = "预警频率")
    private Integer frequency;

    @Schema(description = "通知方式")
    private Integer warnType;

    public static AlertMessageExportVO of(AlertMessageDO entity){
        if(entity == null){
            return null;
        }
        AlertMessageExportVO exportVO = new AlertMessageExportVO();
        BeanUtils.copyProperties(entity, exportVO);
        
        // 转换是否关联规则
        if (entity.getRelated() != null) {
            exportVO.setRelatedText(entity.getRelated() ? "是" : "否");
        }
        
        // 转换预警频率
        if (entity.getFrequency() != null) {
            exportVO.setFrequencyText(entity.getFrequency() == 0 ? "不限制" : entity.getFrequency().toString());
        }
        
        // 转换通知方式
        if (entity.getWarnType() != null) {
            switch (entity.getWarnType()) {
                case 1:
                    exportVO.setWarnTypeText("钉钉通知");
                    break;
                case 2:
                    exportVO.setWarnTypeText("短信通知");
                    break;
                case 3:
                    exportVO.setWarnTypeText("钉钉+短信");
                    break;
                default:
                    exportVO.setWarnTypeText("未知");
                    break;
            }
        }
        
        return exportVO;
    }

}
