package com.tool.converge.repository.domain.alert.vo;

import com.tool.converge.repository.domain.alert.db.AlertModelConfigDO;
import com.tool.converge.repository.domain.alert.db.AlertModelNotifiersDO;
import com.tool.converge.repository.domain.alert.db.AlertModelRuleDO;
import com.tool.converge.repository.domain.rules.db.RulesDO;
import com.tool.converge.repository.domain.system.db.DingTalkUserDO;
import java.io.Serializable;
import java.util.List;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 完整预警消息信息复合VO(用于关联查询)
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-06
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(name = "AlertMessageFullInfoCompositeVO对象", description = "完整预警消息信息复合VO(用于关联查询)")
public class AlertMessageFullInfoCompositeVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "预警配置信息")
    private AlertModelConfigDO alertModelConfig;

    @Schema(description = "通知人员信息列表")
    private List<AlertModelNotifiersDO> notifiers;

    @Schema(description = "用户详细信息列表")
    private List<DingTalkUserDO> users;

    @Schema(description = "模型规则关联列表")
    private List<AlertModelRuleDO> modelRules;

    @Schema(description = "规则信息列表")
    private List<RulesDO> rules;
}