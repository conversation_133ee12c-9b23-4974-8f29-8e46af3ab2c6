package com.tool.converge.repository.domain.alert.vo;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.tool.converge.repository.domain.alert.db.AlertMessageDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;


/**
 * <p>
 * 预警消息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "AlertMessagePageVO对象", description = "预警消息")
public class AlertMessagePageVO implements Serializable{

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "预警事件编号，SDK生成")
    private String eventId;

    @Schema(description = "模型编码")
    private String modelCode;

    @Schema(description = "预警名称")
    private String modelName;

    @Schema(description = "预警类型")
    private String alertType;

    @Schema(description = "第三方名称")
    private String platformName;

    @Schema(description = "期次")
    private String period;

    @Schema(description = "业务唯一编号")
    private String serviceNo;

    @Schema(description = "业务类型")
    private String businessType;

    @Schema(description = "状态值， 例如：单状态/还款状态/ 支付状态/推送状态")
    private String state;

    @Schema(description = "指标值")
    private String indexValue;

    @Schema(description = "原因")
    private String reason;

    @Schema(description = "拓展字段")
    private String payload;

    @Schema(description = "规则id")
    private Long ruleId;

    @Schema(description = "预警规则")
    private String ruleName;

    @Schema(description = "是否关联规则 0为不关联，1为关联")
    private Boolean related;

    @Schema(description = "预警级别")
    private String warnLevel;

    @Schema(description = "系统编号")
    private String systemCode;

    @Schema(description = "系统名称")
    private String systemName;

    @Schema(description = "预警事件时间/上报时间")
    private LocalDateTime alertTime;

    @Schema(description = "预警配置id")
    private Long configId;

    @Schema(description = "预警频率 0为不限制")
    private Integer frequency;

    @Schema(description = "通知方式 1:钉钉，2:短信，3:钉钉+短信")
    private Integer warnType;

    @Schema(description = "通知人员")
    private String notificationUsers;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    private Boolean deleted;

    public static AlertMessagePageVO of(AlertMessageDO entity){
        if(entity == null){
            return null;
        }
        AlertMessagePageVO pageVO = new AlertMessagePageVO();
        BeanUtils.copyProperties(entity, pageVO);
        return pageVO;
    }
}
