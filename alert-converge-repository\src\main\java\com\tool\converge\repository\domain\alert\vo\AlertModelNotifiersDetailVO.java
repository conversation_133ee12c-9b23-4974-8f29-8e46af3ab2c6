package com.tool.converge.repository.domain.alert.vo;

import java.io.Serializable;
import com.tool.converge.repository.domain.alert.db.AlertModelNotifiersDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;


/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05 10:41:50
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "AlertModelNotifiersDetailVO对象", description = "")
public class AlertModelNotifiersDetailVO implements Serializable{

    private static final long serialVersionUID = 1L;
    
    @Schema(description = "通知用户ID")
    private Long userId;

    @Schema(description = "预警配置ID")
    private Long configId;

    public static AlertModelNotifiersDetailVO of(AlertModelNotifiersDO entity){
        if(entity == null){
            return null;
        }
        AlertModelNotifiersDetailVO detailVO = new AlertModelNotifiersDetailVO();
        BeanUtils.copyProperties(entity,detailVO);
        return detailVO;
    }

}
