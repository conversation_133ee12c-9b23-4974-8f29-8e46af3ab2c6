package com.tool.converge.repository.domain.alert.vo;

import java.io.Serializable;
import com.tool.converge.repository.domain.alert.db.AlertModelNotifiersDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;


/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05 10:41:50
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "AlertModelNotifiersPageVO对象", description = "")
public class AlertModelNotifiersPageVO implements Serializable{

    private static final long serialVersionUID = 1L;

    @Schema(description = "通知用户ID")
    private Long userId;

    @Schema(description = "预警配置ID")
    private Long configId;

    public static AlertModelNotifiersPageVO of(AlertModelNotifiersDO entity){
        if(entity == null){
            return null;
        }
        AlertModelNotifiersPageVO pageVO = new AlertModelNotifiersPageVO();
        BeanUtils.copyProperties(entity,pageVO);
        return pageVO;
    }

}
