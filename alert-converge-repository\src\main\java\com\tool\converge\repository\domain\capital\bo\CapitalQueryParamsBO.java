package com.tool.converge.repository.domain.capital.bo;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tool.converge.repository.domain.common.PageParamsBO;
import com.tool.converge.repository.domain.capital.db.CapitalDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * 资方表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-06 10:55:56
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "CapitalQueryParamsBO对象", description = "资方表")
public class CapitalQueryParamsBO extends PageParamsBO<CapitalDO> implements Serializable{

    private static final long serialVersionUID = 1L;


    private Long id;

    @Schema(description = "资方名称")
    private String capitalName;

    @Schema(description = "资方编码")
    private String capitalCode;

    @Schema(description = "创建时间-开始")
    private LocalDateTime createTimeStart;

    @Schema(description = "创建时间-结束")
    private LocalDateTime createTimeEnd;

    @Schema(description = "修改时间-开始")
    private LocalDateTime updateTimeStart;

    @Schema(description = "修改时间-结束")
    private LocalDateTime updateTimeEnd;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    private Boolean deleted;

    @Override
    public LambdaQueryWrapper<CapitalDO> queryWrapper() {

        LambdaQueryWrapper<CapitalDO> query = new LambdaQueryWrapper<>();

        query.eq(id!=null,CapitalDO::getId,id);

        query.eq(StringUtils.isNotBlank(capitalName),CapitalDO::getCapitalName,capitalName);

        query.eq(StringUtils.isNotBlank(capitalCode),CapitalDO::getCapitalCode,capitalCode);

        query.ge(createTimeStart != null, CapitalDO::getCreateTime, createTimeStart);

        query.le(createTimeEnd != null, CapitalDO::getCreateTime, createTimeEnd);

        query.ge(updateTimeStart != null, CapitalDO::getUpdateTime, updateTimeStart);

        query.le(updateTimeEnd != null, CapitalDO::getUpdateTime, updateTimeEnd);

        query.eq(StringUtils.isNotBlank(creator),CapitalDO::getCreator,creator);

        query.eq(StringUtils.isNotBlank(updater),CapitalDO::getUpdater,updater);

        query.eq(deleted!=null,CapitalDO::getDeleted,deleted);

        return query;
    }
}
