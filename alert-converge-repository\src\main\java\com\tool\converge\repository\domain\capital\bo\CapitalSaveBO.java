package com.tool.converge.repository.domain.capital.bo;

import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 资方表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-06 10:55:56
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "CapitalSaveBO对象", description = "资方表")
public class CapitalSaveBO implements Serializable{

    private static final long serialVersionUID = 1L;


    @Schema(description = "资方名称")
    private String capitalName;


    @Schema(description = "资方编码")
    private String capitalCode;


}
