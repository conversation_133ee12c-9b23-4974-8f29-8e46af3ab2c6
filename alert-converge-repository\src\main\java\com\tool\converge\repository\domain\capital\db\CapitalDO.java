package com.tool.converge.repository.domain.capital.db;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 资方表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-06 10:55:56
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_capital")
@Schema(name = "CapitalDO对象", description = "资方表")
public class CapitalDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("`id`")
    private Long id;

    @Schema(description = "资方名称")
    @TableField("`capital_name`")
    private String capitalName;

    @Schema(description = "资方编码")
    @TableField("`capital_code`")
    private String capitalCode;

    @Schema(description = "创建时间")
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    @TableField(value = "`update_time`", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    @TableField(value = "`creator`", fill = FieldFill.INSERT)
    private String creator;

    @Schema(description = "更新人")
    @TableField(value = "`updater`", fill = FieldFill.INSERT_UPDATE)
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    @TableField(value = "`deleted`", fill = FieldFill.INSERT)
    @TableLogic
    private Boolean deleted;


}
