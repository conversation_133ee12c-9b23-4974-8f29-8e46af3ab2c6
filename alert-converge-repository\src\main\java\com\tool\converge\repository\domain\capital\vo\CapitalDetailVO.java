package com.tool.converge.repository.domain.capital.vo;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.tool.converge.repository.domain.capital.db.CapitalDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;


/**
 * <p>
 * 资方表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-06 10:55:56
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "CapitalDetailVO对象", description = "资方表")
public class CapitalDetailVO implements Serializable{

    private static final long serialVersionUID = 1L;
    
    private Long id;

    @Schema(description = "资方名称")
    private String capitalName;

    @Schema(description = "资方编码")
    private String capitalCode;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    private Boolean deleted;

    public static CapitalDetailVO of(CapitalDO entity){
        if(entity == null){
            return null;
        }
        CapitalDetailVO detailVO = new CapitalDetailVO();
        BeanUtils.copyProperties(entity,detailVO);
        return detailVO;
    }

}
