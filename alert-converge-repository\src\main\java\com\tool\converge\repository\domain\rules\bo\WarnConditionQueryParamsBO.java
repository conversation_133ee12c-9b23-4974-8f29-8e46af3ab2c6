package com.tool.converge.repository.domain.rules.bo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tool.converge.repository.domain.common.PageParamsBO;
import com.tool.converge.repository.domain.rules.db.WarnConditionDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * 预警条件表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04 16:55:50
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "WarnConditionQueryParamsBO对象", description = "预警条件表")
public class WarnConditionQueryParamsBO extends PageParamsBO<WarnConditionDO> implements Serializable{

    private static final long serialVersionUID = 1L;


    @Schema(description = "条件id")
    private Long id;

    @Schema(description = "规则id")
    private Long rulesId;

    @Schema(description = "设置项")
    private String settingItem;

    @Schema(description = "运算符")
    private String operator;

    @Schema(description = "赋值项")
    private BigDecimal assignmentItem;

    @Schema(description = "创建时间-开始")
    private LocalDateTime createTimeStart;

    @Schema(description = "创建时间-结束")
    private LocalDateTime createTimeEnd;

    @Schema(description = "修改时间-开始")
    private LocalDateTime updateTimeStart;

    @Schema(description = "修改时间-结束")
    private LocalDateTime updateTimeEnd;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    private Boolean deleted;

    @Override
    public LambdaQueryWrapper<WarnConditionDO> queryWrapper() {

        LambdaQueryWrapper<WarnConditionDO> query = new LambdaQueryWrapper<>();

        query.eq(id!=null,WarnConditionDO::getId,id);

        query.eq(rulesId!=null,WarnConditionDO::getRulesId,rulesId);

        query.eq(StringUtils.isNotBlank(settingItem),WarnConditionDO::getSettingItem,settingItem);

        query.eq(StringUtils.isNotBlank(operator),WarnConditionDO::getOperator,operator);

        query.eq(assignmentItem!=null,WarnConditionDO::getAssignmentItem,assignmentItem);

        query.ge(createTimeStart != null, WarnConditionDO::getCreateTime, createTimeStart);

        query.le(createTimeEnd != null, WarnConditionDO::getCreateTime, createTimeEnd);

        query.ge(updateTimeStart != null, WarnConditionDO::getUpdateTime, updateTimeStart);

        query.le(updateTimeEnd != null, WarnConditionDO::getUpdateTime, updateTimeEnd);

        query.eq(StringUtils.isNotBlank(creator),WarnConditionDO::getCreator,creator);

        query.eq(StringUtils.isNotBlank(updater),WarnConditionDO::getUpdater,updater);

        query.eq(deleted!=null,WarnConditionDO::getDeleted,deleted);

        return query;
    }
}
