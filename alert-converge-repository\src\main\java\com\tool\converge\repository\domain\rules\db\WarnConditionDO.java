package com.tool.converge.repository.domain.rules.db;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 预警条件表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04 16:55:50
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_warn_condition")
@Schema(name = "WarnConditionDO对象", description = "预警条件表")
public class WarnConditionDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "条件id")
    @TableId("`id`")
    private Long id;

    @Schema(description = "规则id")
    @TableField("`rules_id`")
    private Long rulesId;

    @Schema(description = "设置项")
    @TableField("`setting_item`")
    private String settingItem;

    @Schema(description = "运算符")
    @TableField("`operator`")
    private String operator;

    @Schema(description = "赋值项")
    @TableField("`assignment_item`")
    private BigDecimal assignmentItem;

    @Schema(description = "创建时间")
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    @TableField(value = "`update_time`", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    @TableField(value = "`creator`", fill = FieldFill.INSERT)
    private String creator;

    @Schema(description = "更新人")
    @TableField(value = "`updater`", fill = FieldFill.INSERT_UPDATE)
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    @TableField(value = "`deleted`", fill = FieldFill.INSERT)
    @TableLogic
    private Boolean deleted;


}
