package com.tool.converge.repository.domain.source.bo;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tool.converge.repository.domain.common.PageParamsBO;
import com.tool.converge.repository.domain.source.db.DataSourceDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * 数据源
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10 11:00:59
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "DataSourceQueryParamsBO对象", description = "数据源")
public class DataSourceQueryParamsBO extends PageParamsBO<DataSourceDO> implements Serializable{

    private static final long serialVersionUID = 1L;


    @Schema(description = "数据源id")
    private Long id;

    @Schema(description = "数据源类型")
    private String sourceType;

    @Schema(description = "系统名称")
    private String systemName;

    @Schema(description = "系统编号")
    private String systemCode;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "创建时间-开始")
    private LocalDateTime createTimeStart;

    @Schema(description = "创建时间-结束")
    private LocalDateTime createTimeEnd;

    @Schema(description = "修改时间-开始")
    private LocalDateTime updateTimeStart;

    @Schema(description = "修改时间-结束")
    private LocalDateTime updateTimeEnd;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    private Boolean deleted;

    @Override
    public LambdaQueryWrapper<DataSourceDO> queryWrapper() {

        LambdaQueryWrapper<DataSourceDO> query = new LambdaQueryWrapper<>();

        query.like(id!=null,DataSourceDO::getId,id);

        query.eq(StringUtils.isNotBlank(sourceType),DataSourceDO::getSourceType,sourceType);

        query.like(StringUtils.isNotBlank(systemName),DataSourceDO::getSystemName,systemName);

        query.like(StringUtils.isNotBlank(systemCode),DataSourceDO::getSystemCode,systemCode);

        query.eq(StringUtils.isNotBlank(description),DataSourceDO::getDescription,description);

        query.ge(createTimeStart != null, DataSourceDO::getCreateTime, createTimeStart);

        query.le(createTimeEnd != null, DataSourceDO::getCreateTime, createTimeEnd);

        query.ge(updateTimeStart != null, DataSourceDO::getUpdateTime, updateTimeStart);

        query.le(updateTimeEnd != null, DataSourceDO::getUpdateTime, updateTimeEnd);

        query.eq(StringUtils.isNotBlank(creator),DataSourceDO::getCreator,creator);

        query.eq(StringUtils.isNotBlank(updater),DataSourceDO::getUpdater,updater);

        query.eq(deleted!=null,DataSourceDO::getDeleted,deleted);

        return query;
    }
}
