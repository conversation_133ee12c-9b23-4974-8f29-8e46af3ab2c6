package com.tool.converge.repository.domain.source.bo;

import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 数据源
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10 11:00:59
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "DataSourceUpdateBO对象", description = "数据源")
public class DataSourceUpdateBO implements Serializable{

    private static final long serialVersionUID = 1L;


    @Schema(description = "数据源id")
    private Long id;

    @Schema(description = "数据源类型")
    private String sourceType;

    @Schema(description = "系统名称")
    private String systemName;

    @Schema(description = "描述")
    private String description;

}
