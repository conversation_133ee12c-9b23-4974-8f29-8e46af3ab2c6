package com.tool.converge.repository.domain.source.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
  * <AUTHOR>
  * @date 2025/3/20 10:59
  */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "ExistsSystemCodeBo对象", description = "判断系统是否存在")
public class ExistsSystemCodeBo {

    @Schema(description = "系统编号")
    private String systemCode;
}
