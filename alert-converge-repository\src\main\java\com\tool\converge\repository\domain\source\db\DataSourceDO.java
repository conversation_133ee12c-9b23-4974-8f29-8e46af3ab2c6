package com.tool.converge.repository.domain.source.db;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 数据源
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10 11:00:59
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_data_source")
@Schema(name = "DataSourceDO对象", description = "数据源")
public class DataSourceDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "数据源id")
    @TableId("`id`")
    private Long id;

    @Schema(description = "数据源类型")
    @TableField("`source_type`")
    private String sourceType;

    @Schema(description = "系统名称")
    @TableField("`system_name`")
    private String systemName;

    @Schema(description = "系统编号")
    @TableField("`system_code`")
    private String systemCode;

    @Schema(description = "描述")
    @TableField("`description`")
    private String description;

    @Schema(description = "创建时间")
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    @TableField(value = "`update_time`", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    @TableField(value = "`creator`", fill = FieldFill.INSERT)
    private String creator;

    @Schema(description = "更新人")
    @TableField(value = "`updater`", fill = FieldFill.INSERT_UPDATE)
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    @TableField(value = "`deleted`", fill = FieldFill.INSERT)
    @TableLogic
    private Boolean deleted;


}
