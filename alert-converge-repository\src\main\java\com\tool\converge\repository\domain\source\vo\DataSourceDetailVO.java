package com.tool.converge.repository.domain.source.vo;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.tool.converge.repository.domain.source.db.DataSourceDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;


/**
 * <p>
 * 数据源
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10 11:00:59
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "DataSourceDetailVO对象", description = "数据源")
public class DataSourceDetailVO implements Serializable{

    private static final long serialVersionUID = 1L;
    
    @Schema(description = "数据源id")
    private Long id;

    @Schema(description = "数据源类型")
    private String sourceType;

    @Schema(description = "系统名称")
    private String systemName;

    @Schema(description = "系统编号")
    private String systemCode;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    private Boolean deleted;

    public static DataSourceDetailVO of(DataSourceDO entity){
        if(entity == null){
            return null;
        }
        DataSourceDetailVO detailVO = new DataSourceDetailVO();
        BeanUtils.copyProperties(entity,detailVO);
        return detailVO;
    }

}
