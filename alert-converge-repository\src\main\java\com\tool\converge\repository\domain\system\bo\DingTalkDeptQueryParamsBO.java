package com.tool.converge.repository.domain.system.bo;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tool.converge.repository.domain.common.PageParamsBO;
import com.tool.converge.repository.domain.system.db.DingTalkDeptDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * 钉钉部门
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-07 08:48:38
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "DingTalkDeptQueryParamsBO对象", description = "钉钉部门")
public class DingTalkDeptQueryParamsBO extends PageParamsBO<DingTalkDeptDO> implements Serializable{

    private static final long serialVersionUID = 1L;


    @Schema(description = "id")
    private Long id;

    @Schema(description = "父部门id")
    private Long parentId;

    @Schema(description = "部门id")
    private Long deptId;

    @Schema(description = "部门名称")
    private String deptName;

    @Schema(description = "创建时间-开始")
    private LocalDateTime createTimeStart;

    @Schema(description = "创建时间-结束")
    private LocalDateTime createTimeEnd;

    @Schema(description = "修改时间-开始")
    private LocalDateTime updateTimeStart;

    @Schema(description = "修改时间-结束")
    private LocalDateTime updateTimeEnd;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "是否删除 0未删除 null已删除")
    private Boolean deleted;

    @Override
    public LambdaQueryWrapper<DingTalkDeptDO> queryWrapper() {

        LambdaQueryWrapper<DingTalkDeptDO> query = new LambdaQueryWrapper<>();

        query.eq(id!=null,DingTalkDeptDO::getId,id);

        query.eq(parentId!=null,DingTalkDeptDO::getParentId,parentId);

        query.eq(deptId!=null,DingTalkDeptDO::getDeptId,deptId);

        query.eq(StringUtils.isNotBlank(deptName),DingTalkDeptDO::getDeptName,deptName);

        query.ge(createTimeStart != null, DingTalkDeptDO::getCreateTime, createTimeStart);

        query.le(createTimeEnd != null, DingTalkDeptDO::getCreateTime, createTimeEnd);

        query.ge(updateTimeStart != null, DingTalkDeptDO::getUpdateTime, updateTimeStart);

        query.le(updateTimeEnd != null, DingTalkDeptDO::getUpdateTime, updateTimeEnd);

        query.eq(StringUtils.isNotBlank(creator),DingTalkDeptDO::getCreator,creator);

        query.eq(StringUtils.isNotBlank(updater),DingTalkDeptDO::getUpdater,updater);

        query.eq(deleted!=null,DingTalkDeptDO::getDeleted,deleted);

        return query;
    }
}
