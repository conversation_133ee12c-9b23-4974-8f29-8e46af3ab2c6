package com.tool.converge.repository.domain.system.bo;

import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 钉钉部门
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-07 08:48:38
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "DingTalkDeptSaveBO对象", description = "钉钉部门")
public class DingTalkDeptSaveBO implements Serializable{

    private static final long serialVersionUID = 1L;


    @Schema(description = "父部门id")
    private Long parentId;


    @Schema(description = "部门id")
    private Long deptId;


    @Schema(description = "部门名称")
    private String deptName;


}
