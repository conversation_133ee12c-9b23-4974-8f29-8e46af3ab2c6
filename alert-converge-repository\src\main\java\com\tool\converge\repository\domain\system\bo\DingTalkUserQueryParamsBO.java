package com.tool.converge.repository.domain.system.bo;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tool.converge.repository.domain.common.PageParamsBO;
import com.tool.converge.repository.domain.system.db.DingTalkUserDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * 钉钉用户
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-07 08:48:38
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "DingTalkUserQueryParamsBO对象", description = "钉钉用户")
public class DingTalkUserQueryParamsBO extends PageParamsBO<DingTalkUserDO> implements Serializable{

    private static final long serialVersionUID = 1L;


    @Schema(description = "id")
    private Long id;

    @Schema(description = "手机或者电话")
    private String nameOrMobile;

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "部门id")
    private Long deptId;

    @Schema(description = "部门名称")
    private String deptName;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "岗位")
    private String post;

    @Schema(description = "手机")
    private String mobile;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "创建时间-开始")
    private LocalDateTime createTimeStart;

    @Schema(description = "创建时间-结束")
    private LocalDateTime createTimeEnd;

    @Schema(description = "修改时间-开始")
    private LocalDateTime updateTimeStart;

    @Schema(description = "修改时间-结束")
    private LocalDateTime updateTimeEnd;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "是否删除 0未删除 null已删除")
    private Boolean deleted;

    @Override
    public LambdaQueryWrapper<DingTalkUserDO> queryWrapper() {

        LambdaQueryWrapper<DingTalkUserDO> query = new LambdaQueryWrapper<>();

        query.eq(id!=null,DingTalkUserDO::getId,id);

        query.eq(deptId!=null,DingTalkUserDO::getDeptId,deptId);

        query.eq(StringUtils.isNotBlank(deptName),DingTalkUserDO::getDeptName,deptName);

        query.eq(StringUtils.isNotBlank(name),DingTalkUserDO::getName,name);

        query.and(StringUtils.isNotBlank(nameOrMobile),lqw->lqw.like(DingTalkUserDO::getName,nameOrMobile)
                .or().like(DingTalkUserDO::getMobile,nameOrMobile));

        query.eq(StringUtils.isNotBlank(post),DingTalkUserDO::getPost,post);

        query.eq(StringUtils.isNotBlank(mobile),DingTalkUserDO::getMobile,mobile);

        query.eq(StringUtils.isNotBlank(email),DingTalkUserDO::getEmail,email);

        query.ge(createTimeStart != null, DingTalkUserDO::getCreateTime, createTimeStart);

        query.le(createTimeEnd != null, DingTalkUserDO::getCreateTime, createTimeEnd);

        query.ge(updateTimeStart != null, DingTalkUserDO::getUpdateTime, updateTimeStart);

        query.le(updateTimeEnd != null, DingTalkUserDO::getUpdateTime, updateTimeEnd);

        query.eq(StringUtils.isNotBlank(creator),DingTalkUserDO::getCreator,creator);

        query.eq(StringUtils.isNotBlank(updater),DingTalkUserDO::getUpdater,updater);

        query.eq(deleted!=null,DingTalkUserDO::getDeleted,deleted);

        return query;
    }
}
