package com.tool.converge.repository.domain.system.bo;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tool.converge.repository.domain.common.PageParamsBO;
import com.tool.converge.repository.domain.system.db.SysDictKeyDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * 字典配置项
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12 11:12:21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "SysDictKeyQueryParamsBO对象", description = "字典配置项")
public class SysDictKeyQueryParamsBO extends PageParamsBO<SysDictKeyDO> implements Serializable{

    private static final long serialVersionUID = 1L;


    @Schema(description = "id")
    private Long id;

    @Schema(description = "字典编码")
    private String keyName;

    @Schema(description = "字典名称")
    private String keyLabel;

    @Schema(description = "是否启用 0停用 1启用")
    private Boolean status;

    @Schema(description = "字典描述")
    private String description;

    @Schema(description = "创建时间-开始")
    private LocalDateTime createTimeStart;

    @Schema(description = "创建时间-结束")
    private LocalDateTime createTimeEnd;

    @Schema(description = "修改时间-开始")
    private LocalDateTime updateTimeStart;

    @Schema(description = "修改时间-结束")
    private LocalDateTime updateTimeEnd;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    private Boolean deleted;

    @Override
    public LambdaQueryWrapper<SysDictKeyDO> queryWrapper() {

        LambdaQueryWrapper<SysDictKeyDO> query = new LambdaQueryWrapper<>();

        query.eq(id!=null,SysDictKeyDO::getId,id);

        query.like(StringUtils.isNotBlank(keyName),SysDictKeyDO::getKeyName,keyName);

        query.like(StringUtils.isNotBlank(keyLabel),SysDictKeyDO::getKeyLabel,keyLabel);

        query.eq(status!=null,SysDictKeyDO::getStatus,status);

        query.like(StringUtils.isNotBlank(description),SysDictKeyDO::getDescription,description);

        query.ge(createTimeStart != null, SysDictKeyDO::getCreateTime, createTimeStart);

        query.le(createTimeEnd != null, SysDictKeyDO::getCreateTime, createTimeEnd);

        query.ge(updateTimeStart != null, SysDictKeyDO::getUpdateTime, updateTimeStart);

        query.le(updateTimeEnd != null, SysDictKeyDO::getUpdateTime, updateTimeEnd);

        query.eq(StringUtils.isNotBlank(creator),SysDictKeyDO::getCreator,creator);

        query.eq(StringUtils.isNotBlank(updater),SysDictKeyDO::getUpdater,updater);

        query.eq(deleted!=null,SysDictKeyDO::getDeleted,deleted);

        return query;
    }
}
