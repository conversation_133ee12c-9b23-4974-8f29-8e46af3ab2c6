package com.tool.converge.repository.domain.system.bo;

import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * 字典配置项
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12 11:12:21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "SysDictKeyUpdateBO对象", description = "字典配置项")
public class SysDictKeyUpdateBO implements Serializable{

    private static final long serialVersionUID = 1L;


    @Schema(description = "id")
    private Long id;

    @Schema(description = "字典编码")
    @NotBlank(message = "字典编码不能为空")
    private String keyName;

    @NotBlank(message = "字典名称不能为空")
    @Schema(description = "字典名称")
    private String keyLabel;

    @Schema(description = "是否启用 0停用 1启用")
    private Boolean status;

    @Schema(description = "字典描述")
    private String description;

}
