package com.tool.converge.repository.domain.system.bo;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tool.converge.repository.domain.common.PageParamsBO;
import com.tool.converge.repository.domain.system.db.SysDictValueDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * 字典配置值
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12 11:12:21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "SysDictValueQueryParamsBO对象", description = "字典配置值")
public class SysDictValueQueryParamsBO extends PageParamsBO<SysDictValueDO> implements Serializable{

    private static final long serialVersionUID = 1L;


    @Schema(description = "id")
    private Long id;

    @Schema(description = "字典id")
    private Long keyId;

    @Schema(description = "字典名称")
    private String keyName;

    @Schema(description = "是否启用 0停用 1启用")
    private Boolean status;

    @Schema(description = "字典值")
    private String value;

    @Schema(description = "字典项")
    private String label;

    @Schema(description = "说明")
    private String description;

    @Schema(description = "创建时间-开始")
    private LocalDateTime createTimeStart;

    @Schema(description = "创建时间-结束")
    private LocalDateTime createTimeEnd;

    @Schema(description = "修改时间-开始")
    private LocalDateTime updateTimeStart;

    @Schema(description = "修改时间-结束")
    private LocalDateTime updateTimeEnd;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    private Boolean deleted;

    @Override
    public LambdaQueryWrapper<SysDictValueDO> queryWrapper() {

        LambdaQueryWrapper<SysDictValueDO> query = new LambdaQueryWrapper<>();

        query.eq(id!=null,SysDictValueDO::getId,id);

        query.eq(keyId!=null,SysDictValueDO::getKeyId,keyId);

        query.like(StringUtils.isNotBlank(keyName),SysDictValueDO::getKeyName,keyName);

        query.eq(status!=null,SysDictValueDO::getStatus,status);

        query.eq(StringUtils.isNotBlank(value),SysDictValueDO::getValue,value);

        query.like(StringUtils.isNotBlank(label),SysDictValueDO::getLabel,label);

        query.ge(createTimeStart != null, SysDictValueDO::getCreateTime, createTimeStart);

        query.le(createTimeEnd != null, SysDictValueDO::getCreateTime, createTimeEnd);

        query.ge(updateTimeStart != null, SysDictValueDO::getUpdateTime, updateTimeStart);
        query.le(updateTimeEnd != null, SysDictValueDO::getUpdateTime, updateTimeEnd);

        query.eq(StringUtils.isNotBlank(creator),SysDictValueDO::getCreator,creator);

        query.eq(StringUtils.isNotBlank(updater),SysDictValueDO::getUpdater,updater);

        query.eq(deleted!=null,SysDictValueDO::getDeleted,deleted);
        return query;
    }
}
