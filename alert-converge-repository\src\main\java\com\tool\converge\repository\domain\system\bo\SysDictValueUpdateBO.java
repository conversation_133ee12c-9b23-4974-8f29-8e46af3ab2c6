package com.tool.converge.repository.domain.system.bo;

import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <p>
 * 字典配置值
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12 11:12:21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "SysDictValueUpdateBO对象", description = "字典配置值")
public class SysDictValueUpdateBO implements Serializable{

    private static final long serialVersionUID = 1L;


    @Schema(description = "id")
    private Long id;

    @Schema(description = "字典id")
    @NotNull(message = "字典id不能为空")
    private Long keyId;

    @Schema(description = "是否启用 0停用 1启用")
    private Boolean status;

    @Schema(description = "字典值")
    @NotBlank(message = "字典值不能为空")
    private String value;

    @Schema(description = "字典项")
    @NotBlank(message = "字典项不能为空")
    private String label;

    @Schema(description = "说明")
    private String description;


    @Schema(description = "排序")
    private Integer sort;

}
