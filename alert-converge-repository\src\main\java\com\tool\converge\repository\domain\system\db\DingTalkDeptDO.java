package com.tool.converge.repository.domain.system.db;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 钉钉部门
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-07 08:48:38
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_ding_talk_dept")
@Schema(name = "DingTalkDeptDO对象", description = "钉钉部门")
public class DingTalkDeptDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "id")
    @TableId("`id`")
    private Long id;

    @Schema(description = "父部门id")
    @TableField("`parent_id`")
    private Long parentId;

    @Schema(description = "部门id")
    @TableField("`dept_id`")
    private Long deptId;

    @Schema(description = "部门名称")
    @TableField("`dept_name`")
    private String deptName;

    @Schema(description = "创建时间")
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    @TableField(value = "`update_time`", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    @TableField(value = "`creator`", fill = FieldFill.INSERT)
    private String creator;

    @Schema(description = "更新人")
    @TableField(value = "`updater`", fill = FieldFill.INSERT_UPDATE)
    private String updater;

    @Schema(description = "是否删除 0未删除 null已删除")
    @TableField(value = "`deleted`", fill = FieldFill.INSERT)
    @TableLogic(value = "0", delval = "NULL")
    private Boolean deleted;


}
