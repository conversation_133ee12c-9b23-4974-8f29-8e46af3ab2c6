package com.tool.converge.repository.domain.system.db;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 字典配置项
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12 11:12:21
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_sys_dict_key")
@Schema(name = "SysDictKeyDO对象", description = "字典配置项")
public class SysDictKeyDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "id")
    @TableId(value = "`id`")
    private Long id;

    @Schema(description = "字典编码")
    @TableField("`key_name`")
    private String keyName;

    @Schema(description = "字典名称")
    @TableField("`key_label`")
    private String keyLabel;

    @Schema(description = "是否启用 0停用 1启用")
    @TableField("`status`")
    private Boolean status;

    @Schema(description = "字典描述")
    @TableField("`description`")
    private String description;

    @Schema(description = "创建时间")
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    @TableField(value = "`update_time`", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    @TableField(value = "`creator`", fill = FieldFill.INSERT)
    private String creator;

    @Schema(description = "更新人")
    @TableField(value = "`updater`", fill = FieldFill.INSERT_UPDATE)
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    @TableField(value = "`deleted`", fill = FieldFill.INSERT)
    @TableLogic
    private Boolean deleted;


}
