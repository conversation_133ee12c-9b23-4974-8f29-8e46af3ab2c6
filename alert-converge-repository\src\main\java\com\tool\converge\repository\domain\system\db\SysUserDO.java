package com.tool.converge.repository.domain.system.db;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 人员
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-06
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_sys_user")
@Schema(name = "SysUserDO对象", description = "人员")
public class SysUserDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "id")
    @TableId("`user_id`")
    private Long userId;

    @Schema(description = "部门ID")
    @TableField("`dept_id`")
    private Long deptId;

    @Schema(description = "用户账号")
    @TableField("`user_name`")
    private String userName;

    @Schema(description = "用户昵称")
    @TableField("`nick_name`")
    private String nickName;

    @Schema(description = "用户类型（sys_user系统用户）")
    @TableField("`user_type`")
    private String userType;

    @Schema(description = "用户邮箱")
    @TableField("`email`")
    private String email;

    @Schema(description = "手机号码")
    @TableField("`phonenumber`")
    private String phonenumber;

    @Schema(description = "用户性别（0男 1女 2未知）")
    @TableField("`sex`")
    private String sex;

    @Schema(description = "头像地址")
    @TableField("`avatar`")
    private String avatar;

    @Schema(description = "密码")
    @TableField("`password`")
    private String password;

    @Schema(description = "git作者名")
    @TableField("`git_author_name`")
    private String gitAuthorName;

    @Schema(description = "git提交人名")
    @TableField("`git_committer_name`")
    private String gitCommitterName;

    @Schema(description = "禅道用户名")
    @TableField("`zt_user_name`")
    private String ztUserName;

    @Schema(description = "钉钉用户id")
    @TableField("`dingtalk_user_id`")
    private String dingtalkUserId;

    @Schema(description = "帐号状态（0正常 1停用）")
    @TableField("`status`")
    private String status;

    @Schema(description = "创建时间")
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    @TableField(value = "`update_time`", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    @TableField(value = "`creator`", fill = FieldFill.INSERT)
    private String creator;

    @Schema(description = "更新人")
    @TableField(value = "`updater`", fill = FieldFill.INSERT_UPDATE)
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    @TableField(value = "`deleted`", fill = FieldFill.INSERT)
    @TableLogic
    private Boolean deleted;
}