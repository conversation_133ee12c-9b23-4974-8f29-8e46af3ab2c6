package com.tool.converge.repository.domain.system.vo;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.tool.converge.repository.domain.system.db.SysDictKeyDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;


/**
 * <p>
 * 字典配置项
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12 11:12:21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "SysDictKeyPageVO对象", description = "字典配置项")
public class SysDictKeyPageVO implements Serializable{

    private static final long serialVersionUID = 1L;

    @Schema(description = "id")
    private Long id;

    @Schema(description = "字典编码")
    private String keyName;

    @Schema(description = "字典名称")
    private String keyLabel;

    @Schema(description = "是否启用 0停用 1启用")
    private Boolean status;

    @Schema(description = "字典描述")
    private String description;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    private Boolean deleted;
    public SysDictKeyPageVO(SysDictKeyDO entity){
        BeanUtils.copyProperties(entity,this);
    }
}
