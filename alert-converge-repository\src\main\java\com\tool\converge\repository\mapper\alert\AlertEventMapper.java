package com.tool.converge.repository.mapper.alert;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tool.converge.repository.domain.alert.db.AlertEventDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tool.converge.repository.domain.alert.vo.AlertEventPageVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 预警事件 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10 10:48:16
 */
@Mapper
public interface AlertEventMapper extends BaseMapper<AlertEventDO> {

     /**
      * 分页查询
      * <AUTHOR>
      * @date 2025/3/11 18:52
      * @param alertEventDO
      * @param page
      * @MethodName selectEventPage
      * @return IPage<AlertEventPageVO>
      */
    IPage<AlertEventDO> selectEventPage(IPage<AlertEventDO> page, @Param("eventDo") AlertEventDO alertEventDO);

     /**
      * 事件详情
      * <AUTHOR>
      * @date 2025/3/11 18:52
      * @param alertEventDO
      * @param page
      * @MethodName selectEventPage
      * @return IPage<AlertEventPageVO>
      */
    List<AlertEventDO> selectEventDetail(@Param("eventDo") AlertEventDO alertEventDO);
}
