package com.tool.converge.repository.mapper.alert;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tool.converge.repository.domain.alert.bo.AlertMessageQueryParamsBO;
import com.tool.converge.repository.domain.alert.db.AlertMessageDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 预警消息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Mapper
public interface AlertMessageMapper extends BaseMapper<AlertMessageDO> {

    /**
     * 分页查询预警消息
     * @param page 分页参数
     * @param queryParamsBO 查询条件
     * @return 分页结果
     */
    IPage<AlertMessageDO> selectMessagePage(IPage<AlertMessageDO> page, @Param("queryParamsBO") AlertMessageQueryParamsBO queryParamsBO);

}
