package com.tool.converge.repository.mapper.alert;

import com.tool.converge.repository.domain.alert.db.AlertModelConfigDO;
import com.tool.converge.repository.domain.alert.vo.AlertMessageFullInfoCompositeVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05 10:41:50
 */
@Mapper
public interface AlertModelConfigMapper extends BaseMapper<AlertModelConfigDO> {

    /**
     * 根据模型ID获取完整的预警配置信息(包括通知人员、规则等关联信息)
     *
     * @param modelId 模型ID
     * @return 完整的预警配置信息
     */
    AlertMessageFullInfoCompositeVO selectFullInfoByModelId(Long modelId);
}
