package com.tool.converge.repository.mapper.capital;

import com.tool.converge.repository.domain.capital.db.CapitalDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 资方表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-06 10:55:56
 */
@Mapper
public interface CapitalMapper extends BaseMapper<CapitalDO> {

    /**
     * 物理删除
     * @param capitalToDeleteIds
     */
    void physicalDeleteBatchIds(@Param("capitalToDeleteIds") List<Long> capitalToDeleteIds);
}
