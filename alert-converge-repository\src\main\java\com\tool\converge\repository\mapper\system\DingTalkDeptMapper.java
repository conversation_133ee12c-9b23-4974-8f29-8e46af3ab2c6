package com.tool.converge.repository.mapper.system;

import com.tool.converge.repository.domain.system.db.DingTalkDeptDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 钉钉部门 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-07 08:48:38
 */
@Mapper
public interface DingTalkDeptMapper extends BaseMapper<DingTalkDeptDO> {

    /**
     * 通过部门id插入或更新
     *
     * @param list
     */
    void insertOrUpdateByDeptId(@Param("list") List<DingTalkDeptDO> list);
}
