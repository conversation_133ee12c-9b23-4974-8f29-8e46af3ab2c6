package com.tool.converge.repository.mapper.system;

import com.tool.converge.repository.domain.system.db.DingTalkUserDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.commons.math3.util.Pair;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 钉钉用户 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-07 08:48:38
 */
@Mapper
public interface DingTalkUserMapper extends BaseMapper<DingTalkUserDO> {

    /**
     * 根据部门id和用户id插入或更新
     *
     * @param userList
     */
    void insertOrUpdateByDeptIdAndUserId(@Param("list") List<DingTalkUserDO> userList);

    /**
     * 根据部门id和用户id删除
     *
     * @param list
     */
    void deleteByDeptIdAndUserId(@Param("list") List<Pair<Long, String>> list);
}
