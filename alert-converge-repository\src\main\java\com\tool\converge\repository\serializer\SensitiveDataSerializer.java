package com.tool.converge.repository.serializer;

import cn.hutool.core.util.DesensitizedUtil;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;

/**
 * @className: SensitiveDataSerializer
 * @author: ya<PERSON><PERSON><PERSON><PERSON>
 * @description: 数据脱敏序列化处理
 * @date: 2025/2/19 11:52
 * @version: 1.0
 */
public class SensitiveDataSerializer extends JsonSerializer<String> {

    @Override
    public void serialize(String value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (value != null) {
            // 使用Hutool的脱敏工具
            gen.writeString(DesensitizedUtil.desensitized(value, DesensitizedUtil.DesensitizedType.PASSWORD));
        } else {
            gen.writeNull();
        }
    }
}
