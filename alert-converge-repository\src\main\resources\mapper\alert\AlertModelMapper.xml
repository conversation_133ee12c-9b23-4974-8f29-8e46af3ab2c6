<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tool.converge.repository.mapper.alert.AlertModelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tool.converge.repository.domain.alert.db.AlertModelDO">
        <id column="id" property="id" />
        <result column="model_name" property="modelName" />
        <result column="model_code" property="modelCode" />
        <result column="business_type" property="businessType" />
        <result column="alert_type" property="alertType" />
        <result column="related" property="related" />
        <result column="warned" property="warned" />
        <result column="description" property="description" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="creator" property="creator" />
        <result column="updater" property="updater" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, model_name, model_code, business_type, alert_type, related, warned, description, create_time, update_time, creator, updater, deleted
    </sql>

</mapper>
