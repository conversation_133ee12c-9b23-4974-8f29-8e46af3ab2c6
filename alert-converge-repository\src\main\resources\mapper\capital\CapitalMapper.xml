<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tool.converge.repository.mapper.capital.CapitalMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tool.converge.repository.domain.capital.db.CapitalDO">
        <id column="id" property="id" />
        <result column="capital_name" property="capitalName" />
        <result column="capital_code" property="capitalCode" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="creator" property="creator" />
        <result column="updater" property="updater" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, capital_name, capital_code, create_time, update_time, creator, updater, deleted
    </sql>

    <delete id="physicalDeleteBatchIds">
        delete from t_capital where id in
        <foreach collection="capitalToDeleteIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
