<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tool.converge.repository.mapper.rules.RulesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tool.converge.repository.domain.rules.db.RulesDO">
        <id column="id" property="id" />
        <result column="rule_name" property="ruleName" />
        <result column="rule_code" property="ruleCode" />
        <result column="rule_status" property="ruleStatus" />
        <result column="rule_matching" property="ruleMatching" />
        <result column="warn_level" property="warnLevel" />
        <result column="apply_investor" property="applyInvestor" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="creator" property="creator" />
        <result column="updater" property="updater" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, rule_name, rule_status, rule_matching, warn_level, apply_investor, create_time, update_time, creator, updater, deleted
    </sql>

</mapper>
