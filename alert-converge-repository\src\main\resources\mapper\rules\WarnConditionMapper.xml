<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tool.converge.repository.mapper.rules.WarnConditionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tool.converge.repository.domain.rules.db.WarnConditionDO">
        <id column="id" property="id" />
        <result column="rules_id" property="rulesId" />
        <result column="setting_item" property="settingItem" />
        <result column="operator" property="operator" />
        <result column="assignment_item" property="assignmentItem" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="creator" property="creator" />
        <result column="updater" property="updater" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, rules_id, setting_item, operator, assignment_item, create_time, update_time, creator, updater, deleted
    </sql>

</mapper>
