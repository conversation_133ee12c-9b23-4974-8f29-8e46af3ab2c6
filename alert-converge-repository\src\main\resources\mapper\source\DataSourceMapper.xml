<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tool.converge.repository.mapper.source.DataSourceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tool.converge.repository.domain.source.db.DataSourceDO">
        <id column="id" property="id" />
        <result column="source_type" property="sourceType" />
        <result column="system_name" property="systemName" />
        <result column="system_code" property="systemCode" />
        <result column="description" property="description" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="creator" property="creator" />
        <result column="updater" property="updater" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, source_type, system_name, system_code, description, create_time, update_time, creator, updater, deleted
    </sql>

</mapper>
