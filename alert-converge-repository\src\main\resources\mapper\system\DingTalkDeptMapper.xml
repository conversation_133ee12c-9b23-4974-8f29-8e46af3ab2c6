<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tool.converge.repository.mapper.system.DingTalkDeptMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tool.converge.repository.domain.system.db.DingTalkDeptDO">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="dept_id" property="deptId"/>
        <result column="dept_name" property="deptName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
        <result column="deleted" property="deleted"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, parent_id, dept_id, dept_name, create_time, update_time, creator, updater, deleted
    </sql>
    <insert id="insertOrUpdateByDeptId">
        INSERT INTO t_ding_talk_dept (
        id,
        parent_id,
        dept_id,
        dept_name,
        create_time,
        update_time,
        creator,
        updater,
        deleted
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id},#{item.parentId},#{item.deptId},#{item.deptName},#{item.createTime},#{item.updateTime},
             #{item.creator},#{item.updater},#{item.deleted})
        </foreach>
        ON DUPLICATE KEY UPDATE
        parent_id = VALUES(parent_id),
        dept_name = VALUES(dept_name),
        update_time = VALUES(update_time),
        updater = VALUES(updater)
    </insert>

</mapper>
