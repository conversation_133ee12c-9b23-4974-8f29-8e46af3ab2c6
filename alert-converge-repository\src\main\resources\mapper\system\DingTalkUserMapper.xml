<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tool.converge.repository.mapper.system.DingTalkUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tool.converge.repository.domain.system.db.DingTalkUserDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="dept_id" property="deptId" />
        <result column="dept_name" property="deptName" />
        <result column="post" property="post" />
        <result column="mobile" property="mobile" />
        <result column="email" property="email" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="creator" property="creator" />
        <result column="updater" property="updater" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, dept_id, dept_name, name, post, mobile, email, create_time, update_time, creator, updater, deleted
    </sql>
    <insert id="insertOrUpdateByDeptIdAndUserId">
        INSERT INTO t_ding_talk_user (
        id,
        user_id,
        dept_id,
        dept_name,
        name,
        post,
        mobile,
        email,
        create_time,
        update_time,
        creator,
        updater,
        deleted
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id},#{item.userId},#{item.deptId},#{item.deptName},#{item.name},#{item.post},#{item.mobile},
             #{item.email},#{item.createTime},#{item.updateTime},#{item.creator},#{item.updater},#{item.deleted})
        </foreach>
        ON DUPLICATE KEY UPDATE
        dept_name = VALUES(dept_name),
        name = VALUES(name),
        post = VALUES(post),
        mobile = VALUES(mobile),
        email = VALUES(email),
        update_time = VALUES(update_time),
        updater = VALUES(updater)
    </insert>
    <delete id="deleteByDeptIdAndUserId">
        UPDATE t_ding_talk_user SET deleted = NULL
        WHERE (dept_id, user_id) NOT IN (
        <foreach collection="list" item="item" separator=",">
            (#{item.key}, #{item.value})
        </foreach>
        )
    </delete>

</mapper>
