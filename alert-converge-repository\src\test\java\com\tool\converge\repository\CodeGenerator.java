package com.tool.converge.repository;

import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import com.baomidou.mybatisplus.generator.config.GlobalConfig;
import com.baomidou.mybatisplus.generator.config.PackageConfig;
import com.baomidou.mybatisplus.generator.config.StrategyConfig;
import com.baomidou.mybatisplus.generator.config.builder.GeneratorBuilder;
import com.baomidou.mybatisplus.generator.config.converts.MySqlTypeConvert;
import com.baomidou.mybatisplus.generator.config.querys.MySqlQuery;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import com.baomidou.mybatisplus.generator.keywords.MySqlKeyWordsHandler;

/**
 * https://gitee.com/baomidou/generator
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2021/7/22
 **/
public class CodeGenerator {
    static String moduleName = "test";
    static String projectPath = "E:\\workspace\\DataMonitorService";
    static String author = "CodeGenerator";
    static String parent_package_name = "com.tool.converge.repository";
    static String[] tableArr = {"t_dict"};


    public static void main(String[] args) {

        DataSourceConfig dataSourceConfig = new DataSourceConfig
                .Builder("********************************************************************************",
                "xx",
                "xx")
                // 类型转换,数据库=》JAVA类型
                .typeConvert(new MySqlTypeConvert())
                // 关键字处理 ,这里选取了mysql5.7文档中的关键字和保留字（含移除）
                .keyWordsHandler(new MySqlKeyWordsHandler())
                // 数据库信息查询类,默认由 dbType 类型决定选择对应数据库内置实现
                .dbQuery(new MySqlQuery())
                .build();

        // 全局配置
        GlobalConfig globalConfig = GeneratorBuilder.globalConfigBuilder()
                //.enableSpringdoc()
                .outputDir(projectPath)
                //.enableSwagger()
                .author(author)
                // 时间策略
                .dateType(DateType.TIME_PACK)
                // 注释日期格式
                //.commentDate("yyyy-MM-dd")
                .build();

        // 包配置
        PackageConfig packageConfig = new PackageConfig
                .Builder()
                .parent(parent_package_name)
                .moduleName("")
                .mapper("mapper."+moduleName)
                .entity("db."+moduleName)
                .service("service."+moduleName)
                .serviceImpl("service."+moduleName+".impl")
                .build();

        // 策略配置
        StrategyConfig strategyConfig = new StrategyConfig
                .Builder()
                .addInclude(tableArr)
                .addTablePrefix("t_")
                .enableSkipView()

                .entityBuilder()
                .naming(NamingStrategy.underline_to_camel)
                .enableLombok()
                .enableChainModel()
                .fileOverride()
                .serviceBuilder()
                .fileOverride()
                //.formatServiceFileName("I%sService")
                //.formatServiceImplFileName("%sService")
                .controllerBuilder()
                .fileOverride()
                .enableRestStyle()

                .mapperBuilder()
                .fileOverride()
                //.enableBaseResultMap()
                //.enableBaseColumnList()
                .build();

        // 代码生成器
        AutoGenerator generator = new AutoGenerator(dataSourceConfig);
        generator.global(globalConfig);
        generator.packageInfo(packageConfig);
        generator.strategy(strategyConfig);
        generator.execute(new FreemarkerTemplateEngine());
    }

}