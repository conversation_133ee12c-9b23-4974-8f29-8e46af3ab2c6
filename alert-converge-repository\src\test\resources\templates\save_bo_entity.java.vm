package ${boPath};

#set($list=["id","creator","createTime", "updater", "updateTime", "deleted"])
#foreach($pkg in ${table.importPackages})
#if(${pkg}=="java.time.LocalDateTime"&&!${existOtherLocalDateTimeField})
#else
#if(${pkg.indexOf("mybatisplus")}==-1)
import ${pkg};
#end
#end
#end
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * $!{table.comment}
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "${saveBO}对象", description = "$!{table.comment}")
public class ${saveBO} implements Serializable{

    private static final long serialVersionUID = 1L;

## ----------  BEGIN 字段循环遍历  ----------
#foreach($field in ${table.fields})
#if($list.contains($field.propertyName)||${field.keyFlag})
#else

#if("$!field.comment" != "")
    @Schema(description = "${field.comment}")
#end
    private ${field.propertyType} ${field.propertyName};

#end
#end

}
