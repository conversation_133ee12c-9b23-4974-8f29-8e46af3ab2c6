package ${package.ServiceImpl};

#set($mapperObjectName=${table.mapperName.substring(0, 1).toLowerCase()}+${table.mapperName.substring(1)})
import ${boPath}.${saveBO};
import ${boPath}.${updateBO};
import ${boPath}.${queryParamsBO};
import ${voPath}.${detailVO};
import ${voPath}.${pageVO};
import ${package.Entity}.${entity};
import ${package.Mapper}.${table.mapperName};
import ${package.Service}.${table.serviceName};
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
/**
 * <p>
 * $!{table.comment} 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 */
@Slf4j
@Service
public class ${table.serviceImplName} extends ServiceImpl<${table.mapperName}, ${entity}> implements ${table.serviceName} {

    @Resource
    private ${table.mapperName} ${mapperObjectName};

    @Override
    public Boolean saveInfo(${saveBO} saveBO){
        ${entity} entity = new ${entity}();
        BeanUtils.copyProperties(saveBO, entity);
        return SqlHelper.retBool(${mapperObjectName}.insert(entity));
    }

    @Override
    public Boolean delInfo(Long id){
        return SqlHelper.retBool(${mapperObjectName}.deleteById(id));
    }

    @Override
    public Boolean updateInfo(${updateBO} updateBO){
        ${entity} entity = new ${entity}();
        BeanUtils.copyProperties(updateBO, entity);
        return SqlHelper.retBool(${mapperObjectName}.updateById(entity));
    }

    @Override
    public ${detailVO} getInfo(Long id){
        ${entity} entity = ${mapperObjectName}.selectById(id);
        return ${detailVO}.of(entity);
    }

    @Override
    public IPage<${pageVO}> getPageInfo(${queryParamsBO} queryParamsBO){
        return ${mapperObjectName}.selectPage(queryParamsBO.pageInfo(), queryParamsBO.queryWrapper()).convert(${pageVO}::of);
    }

}
