<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.qmqb.structure</groupId>
        <artifactId>qmqb-top-dependencies-parent</artifactId>
        <version>2025.1.28-SNAPSHOT</version>
        <relativePath/>
    </parent>

    <groupId>com.tool.converge</groupId>
    <artifactId>alert-converge</artifactId>
    <version>1.0.0.RELEASE</version>
    <packaging>pom</packaging>


    <modules>
        <module>alert-converge-api</module>
        <module>alert-converge-job</module>
        <module>alert-converge-common</module>
        <module>alert-converge-env</module>
        <module>alert-converge-repository</module>
        <module>alert-converge-common-biz</module>
    </modules>

    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <sonar.java.source>1.8</sonar.java.source>
        <knife4j.version>4.4.0</knife4j.version>
        <sso.version>1.1.10-SNAPSHOT</sso.version>
        <block-sql.starter.version>2.0.0-SNAPSHOT</block-sql.starter.version>
        <dingtalk-service-sdk.version>2.0.0</dingtalk-service-sdk.version>

    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.tool.converge</groupId>
                <artifactId>alert-converge-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tool.converge</groupId>
                <artifactId>alert-converge-env</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tool.converge</groupId>
                <artifactId>alert-converge-repository</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tool.converge</groupId>
                <artifactId>alert-converge-common-biz</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!--接口文档-->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-openapi3-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>
            <!-- sql 审查 -->
            <dependency>
                <groupId>com.qmqb.structure</groupId>
                <artifactId>spring-boot-block-sql-starter</artifactId>
                <version>${block-sql.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>alibaba-dingtalk-service-sdk</artifactId>
                <version>${dingtalk-service-sdk.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <!-- 设定主仓库，按设定顺序进行查找。 -->
    <repositories>
        <repository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>hzed-repos</id>
            <name>hzed Repository</name>
            <url>https://nexus.qmwallet.vip/nexus/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
    </repositories>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <activatedProperties>dev</activatedProperties>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>testA</id>
            <properties>
                <activatedProperties>testA</activatedProperties>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <activatedProperties>prod</activatedProperties>
            </properties>
        </profile>
    </profiles>
    <build>
        <plugins>
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
                <version>3.6.0.1398</version>
            </plugin>
        </plugins>
    </build>
</project>